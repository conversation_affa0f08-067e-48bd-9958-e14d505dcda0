# -*- coding: utf-8 -*-
"""
Create Time: 2025/05/26 14:04
Author: liuyigong
Description: 异步文件服务
"""
import json
import asyncio
import os
from typing import Dict, List, Any, Optional
import traceback
from datetime import datetime
from werkzeug.datastructures import FileStorage

from src.config.constant import *
from src.utils.elasticsearch_async_util import asyncElasticSearchEngine
from src.utils.string_util import secure_filename
from src.utils.file_util import calculateMd5File
from src.storage.kbase.parser.pdf.pdf_parser import pdfToStr, getPdfDocList
from src.storage.kbase.parser.txt.TextSegment import textToStr
from src.storage.kbase.parser.json.json_parser import FixedFieldJsonParser
from src.storage.kbase.parser.word.docx_parser import docx_chapter_parser, docx2str
import src.storage.kbase.parser.general_length_split as lengthSplite
import src.storage.kbase.parser.general_chapter_split as chapterSplite
from src.storage.kbase.parser.table.table_parser import tableToJson
from src.calculate.embedding_server import getContentVecAsync
from src.service.firewall.document_service_async import es_insert_data_async
import src.service.firewall.document_service_async as document_service_async


async def doUpload(file: FileStorage, parameters: Dict) -> str:
    """
    异步处理文件上传
    
    文件上传和存储
    :param uploadFile: 文件
    :param kbaseId: 知识库id
    :param model: 分段形式（长度或章节）chapter/length
    :param maxTokens: 分段后每段的字数
    :param chunkOverlap: 上下文保留的字数
    :param lastLevel: 章节分隔时最小等级
    :param mapping: json解析对应关系
    :param merge_content: json解析content是否合并
    :return: 上传结果
    """
    try:
        # 参数验证
        kbaseId = parameters.get("kbaseId")
        model = parameters.get("model", "auto")
        maxTokens = parameters.get("maxTokens", chunks_max_tokens)
        if not maxTokens: maxTokens = chunks_max_tokens
        chunkOverlap = parameters.get("chunkOverlap", chunks_overlap)
        if not chunkOverlap: chunkOverlap = chunks_overlap
        lastLevel = parameters.get("lastLevel", "")
        mapping = parameters.get("mapping", {})
        merge_content = parameters.get("merge_content", True)
        
        log.info(f"【文档上传参数】: {parameters}")
        
        if not file :
            log.info(f"【文档上传结果】:文件未上传")
            return json.dumps({"status": 204, "message": "文件未上传", "data": ""}, ensure_ascii=False)
        
        if not file.filename:
            log.info(f"【文档上传结果】:文件名不能为空")
            return json.dumps({"status": 204, "message": "文件名不能为空", "data": ""}, ensure_ascii=False)
        
        if not kbaseId or not kbaseId.isdigit():
            log.info(f"【文档上传结果】:未选择知识库")
            return json.dumps({"status": 204, "message": "未选择知识库", "data": ""}, ensure_ascii=False)
        
        kbaseId = int(kbaseId)
        
        # 检查知识库是否存在
        exists = await asyncElasticSearchEngine.find_field(
            field="kbase_id", 
            field_value=kbaseId, 
            index_name=es_kbase_index
        )
        
        if not exists:
            log.info(f"【文档上传结果】:知识库不存在")
            return json.dumps({"status": 204, "message": "知识库不存在", "data": ""}, ensure_ascii=False)
        
        # 检查知识库文档数量
        doc_list = await getKbaseDocListAsync(kbaseId)
        if doc_list["aggregations"]["courseAgg"]["value"] >= max_doc_num:
            log.info(f"【文档上传结果】:超过知识库文档最大数量")
            return json.dumps({"status": 204, "message": "超过知识库文档最大数量", "data": kbaseId}, ensure_ascii=False)
        
        if model not in ["chapter", "length", "auto"]:
            log.info(f"【文档上传结果】:未选择分段方式")
            return json.dumps({"status": 204, "message": "未选择分段方式", "data": ""}, ensure_ascii=False)
        
        # 处理maxTokens参数
        if not maxTokens or not str(maxTokens).isdigit():
            log.info(f"【文档上传结果】:分段长度错误")
            return json.dumps({"status": 204, "message": "分段长度错误", "data": ""}, ensure_ascii=False)
        
        # 处理chunkOverlap参数
        if not chunkOverlap or not str(chunkOverlap).isdigit():
            log.info(f"【文档上传结果】:上下文长度错误")
            return json.dumps({"status": 204, "message": "上下文长度错误", "data": ""}, ensure_ascii=False)
        
        if chunkOverlap >= maxTokens:
            log.info(f"【文档上传结果】:上下文长度不得大于分段长度")
            return json.dumps({"status": 204, "message": "上下文长度不得大于分段长度", "data": ""}, ensure_ascii=False)
        
        # 处理lastLevel参数
        if not lastLevel.strip():
            lastLevel = 999
        if not str(lastLevel).isdigit():
            log.info(f"【文档上传结果】:章节最小等级错误")
            return json.dumps({"status": 204, "message": "章节最小等级错误", "data": ""}, ensure_ascii=False)
        lastLevel = int(lastLevel)
        
        
        # --------------------文件保存部分---------------------
        # 转换为安全的文件名称
        filename = secure_filename(file.filename)
        log.info(f"【上传文件】: {filename}")
        
        # 创建知识库文件夹
        kbase_path = os.path.join(kbaseFilePath, str(kbaseId))
        os.makedirs(kbase_path, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(kbase_path, filename)
        # 使用 asyncio.to_thread 包装同步的 save 方法
        await asyncio.to_thread(file.save, file_path)
        log.info(f"【文件保存】: {file_path}")
        # -----------------------------------------------------
        
        # 检查文档是否已存在
        isExist = await isDocInKbaseAsync(kbaseId=kbaseId, fileName=filename, filePath=file_path)
        if isExist in [1, 2]:
            if isExist == 2 and os.path.exists(file_path):
                os.remove(file_path)
            return json.dumps({"status": 202, "message": "文档已存在", "data": filename}, ensure_ascii=False)
        
        # 根据文件类型处理
        if filename.endswith((".xlsx", ".xls", ".csv")):
            model = "table"
            parameters['model'] = "table"
        elif filename.endswith(".json"):
            model = "json"
            parameters['model'] = "json"
        
        # 根据模型处理文件
        # ===================================================
        # -------------------按照规则分隔------------------------
        # -------------------按照章节分隔------------------------
        if model == "chapter":
            uploadInfo = await chapterSpilteAndInsertAsync(kbaseId, file_path, filename, lastLevel, parameters)
        # =======================================================
        # -------------------表格数据处理------------------------
        elif model == "table":
            uploadInfo = await tableSpilteAndInsertAsync(kbaseId, file_path, filename, parameters)
        # =======================================================
        # -------------------JSON数据处理------------------------
        elif model == "json":
            uploadInfo = await jsonSpilteAndInsertAsync(kbaseId, file_path, filename, mapping, parameters, merge_content)
        # =======================================================
        # -------------------智能分隔（先尝试章节，失败后再尝试长度）------------------------
        elif model == "auto":
            # 先尝试章节分割，失败后再尝试长度分割
            uploadInfo = await chapterSpilteAndInsertAsync(kbaseId, file_path, filename, lastLevel, parameters)
            if uploadInfo["status"] != 200:
                log.info(f"【章节分段失败使用长度分段】:{filename}")
                uploadInfo = await lengthSpilteAndInsertAsync(kbaseId, file_path, filename, maxTokens, chunkOverlap)
        else:
            # 按长度分割
            uploadInfo = await lengthSpilteAndInsertAsync(kbaseId, file_path, filename, maxTokens, chunkOverlap)
        
        # 处理上传失败的情况
        if uploadInfo["status"] != 200 and os.path.exists(file_path):
            os.remove(file_path)
            log.info(f"【文件删除】: {file_path}")
        
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)
    except Exception as e:
        log.error(f"文件上传处理失败: {str(e)}\n{traceback.format_exc()}")
        if os.path.exists(file_path):
            os.remove(file_path)
            log.info(f"【文件删除】: {file_path}")
        return json.dumps({"status": 201, "message": f"上传异常: {str(e)}", "data": file.filename if file else ""}, ensure_ascii=False)


async def doList(kbaseId: str) -> str:
    """
    异步获取知识库文档列表
    
    参数:
        kbaseId: 知识库ID
        
    返回:
        文档列表的JSON字符串
    """
    log.info(f"【文件列表获取参数】:{kbaseId}")
    try:
        # 参数验证
        if not kbaseId:
            return json.dumps({"status": 202, "message": "知识库ID不能为空", "data": {}}, ensure_ascii=False)
        
        if not kbaseId.isdigit():
            return json.dumps({"status": 202, "message": "知识库ID格式错误", "data": {}}, ensure_ascii=False)
        
        kbaseId = int(kbaseId)
        
        result = await getKbaseDocListAsync(kbaseId)
        bookInfo = []
        # 解析列表
        for oneBook in result.get("hits", {}).get("hits", []):
            bookInfo.append({
                "docName": oneBook["_source"]["doc_name"],
                "docId": oneBook["_source"]["doc_id"],
                "model": oneBook["_source"]["model"],
                "createTime": oneBook["_source"]["create_time"]
            })
        listInfo = {"status": 200, "message": "", "data": bookInfo}
        log.info(f"【文件列表获取结果】:{listInfo}")
        
        return json.dumps({"status": 200, "message": "获取成功", "data": bookInfo}, ensure_ascii=False)
    except Exception as e:
        log.error(f"获取文档列表失败: {str(e)}")
        log.error(traceback.format_exc())
        return json.dumps({"status": 201, "message": f"获取文档列表失败: {str(e)}", "data": []}, ensure_ascii=False)


async def doDelete(docId: str):
    """
    异步删除文档
    
    参数:
        docId: 文档ID
        kbaseId: 知识库ID
        docName: 文档名称
        
    返回:
        处理结果的JSON字符串
    """
    log.info(f"【文件删除参数】:{docId}")
    # 文档ID校验
    if docId == 0 or not docId.isdigit():
        delInfo = {"status": 204, "message": "未选择文档", "data": ""}
        log.info(f"【文件删除结果】:{delInfo}")
        return json.dumps(delInfo, ensure_ascii=False)
    docId = int(docId)
    try:
        query = {
            "size": 1,
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "doc_id": docId
                            }
                        }
                    ]
                }
            }
        }
       
        result = await asyncElasticSearchEngine.search(query=query, index_name=es_doc_index)
        log.info(f"【文档信息查询结果】: {result}")
        if result["hits"]["total"]["value"] == 0:
            delInfo = {"status": 204, "message": "文档不存在", "data": ""}
            log.info(f"【文件删除结果】:{delInfo}")
            return json.dumps(delInfo, ensure_ascii=False)
        
        fileName = result["hits"]["hits"][0]["_source"]["doc_name"]
        kbaseId = result["hits"]["hits"][0]["_source"]["kbase_id"]
       
        # 删除es中的文档数据
        await document_service_async.delete_doc_async(index_name=es_doc_index, field="doc_id", field_value=docId)
        await document_service_async.es_data_refresh_manually_async(index_name=es_doc_index)
        log.info(f"【文件删除结果】:删除成功")
        
        # 删除文件系统中的文件
        if kbaseId and fileName:
            file_path = os.path.join(kbaseFilePath, str(kbaseId), fileName)
            if os.path.exists(file_path):
                await asyncio.to_thread(os.remove, file_path)
                log.info(f"【文件删除结果】:文件删除成功")
        
        return json.dumps({"status": 200, "message": "删除成功", "data": {}}, ensure_ascii=False)
    except Exception as e:
        log.error(f"删除文档失败: {str(e)}")
        return json.dumps({"status": 202, "message": f"删除文档失败: {str(e)}", "data": {}}, ensure_ascii=False)

async def process_document_chunks(kbaseId: str, fileName: str, filePath: str, chunks: List[Dict]) -> Dict:
    """
    异步处理文档分块并插入数据库
    
    参数:
        kbaseId: 知识库ID
        fileName: 文件名
        filePath: 文件路径
        chunks: 文档分块
        
    返回:
        处理结果
    """
    from src.utils.id_util import genNoRepeatNum10Id
    try:
        # 准备ES和Milvus数据
        es_data = []
        milvus_data = []
        
        # 计算文件MD5
        file_md5 = await asyncio.to_thread(calculateMd5File, filePath)
        
        # 处理每个分块
        for chunk in chunks:
            content = chunk.get("content", "")
            title = chunk.get("title", "")
            summary = chunk.get("summary", "")
            
            # 生成内容ID
            content_id = genNoRepeatNum10Id()
            
            # 构建ES数据
            es_doc = {
                "doc_id": file_md5,
                "doc_name": fileName,
                "content_id": content_id,
                "title": title,
                "content": content,
                "summary": summary,
                "kbase_id": kbaseId,
                "create_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            es_data.append(es_doc)
            
            # 异步获取内容向量
            content_vec = await getContentVecAsync([content])
            
            # 构建Milvus数据
            milvus_doc = {
                "content_id": content_id,
                "doc_id": file_md5,
                "kbase_id": kbaseId,
                "embedding": content_vec[0]
            }
            
            milvus_data.append(milvus_doc)
        
        # 异步插入数据
        result = await es_insert_data_async(
            es_data=es_data,
            index_name=es_doc_index,
            milvus_data=milvus_data,
            collection_name=milvus_index
        )
        
        if result.get("code") == 200:
            return {
                "status": 200,
                "message": "上传成功",
                "data": {
                    "doc_id": file_md5,
                    "doc_name": fileName
                }
            }
        else:
            return {
                "status": 202,
                "message": result.get("msg", "上传失败"),
                "data": {}
            }
    except Exception as e:
        log.error(f"处理文档分块失败: {str(e)}")
        return {
            "status": 202,
            "message": f"处理文档分块失败: {str(e)}",
            "data": {}
        }

async def isDocInKbaseAsync(kbaseId, fileName, filePath):
    '''
    异步判断文档是否在知识库中
    :param kbaseId: 知识库id
    :param fileName: 文档名
    :param filePath: 文件路径
    :return: 存在同名文档返回1，存在同名md5文档返回2， 不存在返回0
    '''

    # 判断文件名是否存在
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "kbase_id": int(kbaseId)
                        }
                    },
                    {
                        "term": {
                            "doc_name_keyword": fileName
                        }
                    }
                ]
            }
        }
    }
    result = await asyncElasticSearchEngine.search(query=query, index_name=es_doc_index)
    if result["hits"]["total"]["value"] > 0:
        return 1

    # 判断文件md5是否存在 - 计算MD5是I/O操作，使用异步
    file_md5 = await asyncio.to_thread(calculateMd5File, filePath)
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "kbase_id": int(kbaseId)
                        }
                    },
                    {
                        "term": {
                            "doc_md5": file_md5
                        }
                    }
                ]
            }
        }
    }
    result = await asyncElasticSearchEngine.search(query=query, index_name=es_doc_index)
    if result["hits"]["total"]["value"] > 0:
        return 2

    return 0

async def getKbaseDocListAsync(kbaseId):
    '''
    异步获取知识库文档列表
    :param kbaseId: 知识库ID
    :return: 文档列表
    '''
    query = {
        "size": 999,
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "kbase_id": kbaseId
                        }
                    }
                ]
            }
        },
        "collapse": {
            "field": "doc_id"
        },
        "from": 0,
        "track_total_hits": True,
        "aggs": {
            "courseAgg": {
                "cardinality": {
                    "field": "doc_id"
                }
            }
        }
    }
    # 使用异步ES引擎执行搜索
    result = await asyncElasticSearchEngine.search(index_name=es_doc_index, query=query)
    log.info(f"【{kbaseId}文档列表异步查询结果】: {len(result)} 条")
    return result

async def chapterSpilteAndInsertAsync(kbaseId, filePath, fileName, lastLevel, parameters):
    '''
        异步按章节解析文档并保存
    :param kbaseId: 知识库id
    :param filePath: 文档全路径
    :param fileName: 文档名
    :param lastLevel: 解析到的最大章节等级
    :return:
    '''
    # 不同类型文档解析为统一格式
    # [ { "title": "xxxx||xxxx||xxx", "content": "xxxxxxxx" } ]
    docDicList = []

    if fileName.endswith("pdf"):
        uploadInfo, docDicList = await asyncio.to_thread(getPdfDocList, filePath, fileName, lastLevel)
    elif fileName.endswith("docx"):
        min_level = -1 if lastLevel == 999 else lastLevel
        uploadInfo, docDicList = await asyncio.to_thread(docx_chapter_parser, filePath, min_level=min_level)
    else:
        uploadInfo = {"status": 202, "message": "暂未支持该类型解析", "data": fileName}

    # 章节分段只有一段说明没有解析到目录
    if len(docDicList) <= 1:
        uploadInfo = {"status": 204, "message": "章节分段解析失败", "data": fileName}

    # 状态码为200说明解析正确，将按照章节切分的进行扩充
    if uploadInfo["status"] == 200 and len(docDicList) != 0:
        # 字段富化，构造入库结构
        esInsertList, milvusInsertList = await chapterSplite.generateInsertDicAsync(
            kbaseId, fileName, filePath, docDicList, parameters
        )
        
        # 入库
        await es_insert_data_async(
            es_data=esInsertList, 
            index_name=es_doc_index,
            milvus_data=milvusInsertList, 
            collection_name=milvus_index
        )
        
        uploadInfo = {
            "status": 200,
            "message": "上传成功",
            "data": {
                "doc_id": esInsertList[0]["doc_id"],
                "doc_name": fileName,
                "model": "chapter"
            }
        }
        if lastLevel != 999:
            uploadInfo["last_level"] = lastLevel
    else:
        uploadInfo = {
            "status": 202,
            "message": "文档解析章节为空",
            "data": {
                "doc_name": fileName
            }
        }
    return uploadInfo

async def tableSpilteAndInsertAsync(kbaseId, filePath, fileName, parameters):
    '''
        异步按表格解析文档并保存
    :param kbaseId: 知识库id
    :param filePath: 文档全路径
    :param fileName: 文档名
    :return:
    '''
    info, tableList = tableToJson(filePath, fileName)
    if info.get("code") != 200 or len(tableList) == 0:
        uploadInfo = {"status": 209, "message": "上传失败", "data": fileName}
    else:
        # 字段富化，构造入库结构
        esInsertList, milvusInsertList = await chapterSplite.generateInsertDicAsync(
            kbaseId, fileName, filePath, tableList, parameters
        )
        
        # 入库
        await es_insert_data_async(
            es_data=esInsertList, 
            index_name=es_doc_index,
            milvus_data=milvusInsertList, 
            collection_name=milvus_index
        )
        
        uploadInfo = {
            "status": 200,
            "message": "上传成功",
            "data": {
                "doc_id": esInsertList[0]["doc_id"],
                "doc_name": fileName,
                "model": "chapter"
            }
        }
    return uploadInfo

async def jsonSpilteAndInsertAsync(kbaseId, filePath, fileName, mapping, parameters, merge_content=True):
    '''
        异步按JSON结构解析文档并保存
    :param kbaseId: 知识库id
    :param filePath: 文档全路径
    :param fileName: 文档名
    :return:
    '''
    # 解析JSON文件
    json_parser = FixedFieldJsonParser()
    info, json_data = await asyncio.to_thread(
        json_parser.parse, 
        filePath=filePath, 
        fileName=fileName, 
        mapping=mapping, 
        merge_content=False
    )

    if info.get("code") != 200 or len(json_data) == 0:
        uploadInfo = {"status": 209, "message": "上传失败", "data": fileName}
    else:
        # 字段富化，构造入库结构 - 使用新的异步方法
        esInsertList, milvusInsertList = await chapterSplite.generateInsertDicAsync(
            kbaseId, fileName, filePath, json_data, parameters
        )

        # 入库
        await es_insert_data_async(
            es_data=esInsertList, 
            index_name=es_doc_index,
            milvus_data=milvusInsertList, 
            collection_name=milvus_index
        )

        uploadInfo = {
            "status": 200,
            "message": "上传成功",
            "data": {
                "doc_id": esInsertList[0]["doc_id"],
                "doc_name": fileName,
                "model": "json"
            }
        }
    return uploadInfo

async def lengthSpilteAndInsertAsync(kbaseId, filePath, fileName, maxTokens, chunkOverlap):
    '''
        异步按长度解析文档并保存
    :param kbaseId: 知识库id
    :param filePath: 文档全路径
    :param fileName: 文档名
    :param maxTokens: 保留块长度
    :param chunkOverlap: 保留上下文长度
    :return:
    '''
    # 不同类型文档均解析为纯字符串
    docStr = ""
    # 根据不同文档类型解析成纯文本
    if fileName.endswith("pdf"):
        uploadInfo, docStr = await asyncio.to_thread(pdfToStr, filePath)
    elif fileName.endswith("txt"):
        uploadInfo, docStr = await asyncio.to_thread(textToStr, filePath)
    elif fileName.endswith("docx"):
        uploadInfo, docStr = await asyncio.to_thread(docx2str, filePath)
    else:
        uploadInfo = {"status": 202, "message": "暂未支持该类型解析", "data": fileName}
    # 状态码为200说明解析正确，将纯文本根据输入参数分段，并格式化向量化
    if uploadInfo["status"] == 200 and docStr.strip() != "":
        # 字段富化，构造入库结构
        esInsertList, milvusInsertList = await lengthSplite.generateInsertDicAsync(
            kbaseId, fileName, filePath, docStr,
            maxTokens, chunkOverlap
        )
        # 入库
        await es_insert_data_async(
            es_data=esInsertList, 
            index_name=es_doc_index,
            milvus_data=milvusInsertList, 
            collection_name=milvus_index
        )
        uploadInfo = {
            "status": 200,
            "message": "上传成功",
            "data": {
                "doc_id": esInsertList[0]["doc_id"],
                "doc_name": fileName,
                "model": "length",
                "max_tokens": maxTokens,
                "chunk_overlap": chunkOverlap
            }
        }
    else:
        uploadInfo = {
            "status": 202,
            "message": "文档解析为空",
            "data": {
                "doc_name": fileName
            }
        }
    return uploadInfo

