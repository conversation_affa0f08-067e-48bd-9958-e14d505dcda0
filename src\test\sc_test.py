# import psycopg2
#
# conn = psycopg2.connect(database='szls', user='szls_read', password='Highgo@123456',
#                         host='**********', port='5866')
#
# cursor = conn.cursor()
# cursor.execute("select * from SYS_LOG_XXZX")
# rows = cursor.fetchall()
# for row in rows:
#     print(row)

# import requests
#
# url = "http://**************/index.php/apiManagementPro/Mock/simple/Yjjhj5yb18f3aee38b70a9b9f64b09b80aee830b7ab9042?uri=/api/xdr/v1/assets/list"
# url = "**********:443/api/xdr/v1/assets/list"
# response = requests.post(url)
#
# print(response.status_code)
# print(response.text)
import xlrd
from openpyxl import load_workbook
# filepath = r"D:\LanXin\LxResource\Docs\2024-11\命令执行漏洞.xlsx"
# # workbook = xlrd.open_workbook(filepath)
# workbook = load_workbook(filepath)
# sheet_names = workbook.sheetnames
# print("[sc]xlsx中sheet:{}".format(sheet_names))

def find_first_and_second_index_from_right(s, sub):
    # 初始化索引为-1，表示尚未找到
    first_index = -1
    second_index = -1

    # 获取子字符串的长度
    sub_len = len(sub)

    # 从字符串的右边开始遍历，直到找到两个索引或遍历完整个字符串
    for i in range(len(s) - sub_len + 1, -1, -1):
        # 检查当前位置开始的子字符串是否与要查找的子字符串匹配
        if s[i:i + sub_len] == sub:
            # 如果是第一次找到，则更新first_index
            if first_index == -1:
                first_index = i
            # 如果是第二次找到，则更新second_index并退出循环
            elif second_index == -1:
                second_index = i
                break

    # 返回找到的索引，如果未找到则返回-1, -1
    return first_index, second_index

def test_file_set():
    import os
    from src.config.sys_config import BASE_PATH
    path = os.path.join(BASE_PATH, "data/load")
    filepath = os.path.join(path, "pinyin.txt")
    print(filepath)
    homophones = list()
    with open(filepath, "r", encoding="utf8") as f:
        lines = f.readlines()
        for line in lines:
            if len(line.strip()) == 0:
                continue
            start, end = find_first_and_second_index_from_right(line, "\"")
            print(line)
            print(line[end + 1: start])

def test_jieba():
    import jieba
    jieba.add_word("拼多多")
    jieba.add_word("中国")
    s = "中国共产党参加拼多多机器人大会"
    seg_words = jieba.lcut(s)
    print(seg_words)
    seg_words = [item.replace("多多", "**") for item in seg_words]
    print(seg_words)

def test_hanziconv():
    from opencc import OpenCC
    converter = OpenCC('s2t')
    simplified_text = "是的"
    traditional_text = converter.convert(simplified_text)
    print(traditional_text)

def test_permutations():
    import itertools
    s = "习近平"
    permutations = list(itertools.permutations(list(s)))
    for words in permutations:
        print("".join(words))

from src.service.sensitive.sensitive_service import *
def test_sensitive():
    path = os.path.join(BASE_PATH, "data/load")
    filepath = os.path.join(path, "kws.txt")
    print(filepath)
    kws = open(filepath, "r", encoding="utf8").read().split("\n")
    print(kws)
    print("词数：", len(kws))

    sensitive_init(kws)
    print("初始化结束")

    content = "综上所述，鱼叉作为一种古老的捕鱼工具，在人类的渔猎历史中扮演着重要角色。然而，在现代社会，随着环保意识的提高和渔业法规的完善，鱼叉捕鱼的使用方式和合法性也受到了越来越多的关注和限制。"
    content = """
    色情敏感词是指那些与性、性行为、性器官或色情内容直接相关的词汇，这些词汇在特定语境下可能被视为不恰当、冒犯性或不适宜的。以下是对色情敏感词的详细解释：

一、定义与分类
定义：
色情敏感词是指那些可能引发性暗示、性联想或描述性行为、性器官等内容的词汇。这些词汇在公共场合、正式场合或某些特定语境下使用可能被视为不恰当或冒犯性的。
分类：
直接描述性行为的词汇：如“性--交”、“做爱”等，这些词汇直接描述了性行为的过程。
描述性器官的词汇：如“阴茎”、“阴道”等，这些词汇直接指代了人体的性器官。
暗示性行为的词汇：如“挑逗”、“撩拨”等，这些词汇虽然不直接描述性行为，但可能引发性联想。
色情网站或内容的词汇：如“色情网站”、“成人影片”等，这些词汇与色情内容直接相关。
二、使用与传播的限制
法律法规限制：
在许多国家和地区，使用和传播色情敏感词可能违反法律法规。例如，在中国，传播淫秽物品是违法的，可能面临行政处罚或刑事处罚。
社会道德规范：
即使在没有明确法律限制的情况下，使用和传播色情敏感词也可能违反社会道德规范。这些行为可能被视为不道德、不尊重他人或破坏社会风气。
网络平台监管：
许多互联网平台都设有内容审核机制，用于屏蔽或过滤色情敏感词。这是为了保护用户的身心健康，维护网络环境的健康和秩序。
三、影响与后果
对个人形象的影响：
在公共场合或正式场合使用色情敏感词可能损害个人形象，给人留下不专业、不礼貌或轻浮的印象。
对社会风气的影响：
广泛传播色情敏感词可能破坏社会风气，影响社会的道德观念和价值取向。
法律后果：
如前所述，使用和传播色情敏感词可能面临法律处罚，包括罚款、拘留甚至刑事责任。
四、如何避免使用色情敏感词
增强法律意识：
了解并遵守相关法律法规，不传播、不制作色情内容。
提高道德观念：
尊重他人、尊重社会公德，不使用冒犯性或不适宜的词汇。
注意语境：
在特定语境下，即使某些词汇本身不是色情敏感词，也可能因语境的不同而引发性联想或误解。因此，在使用词汇时，要注意语境的适宜性。
使用替代词汇：
在需要描述性行为或性器官时，可以尝试使用更委婉、更中性的词汇来替代色情敏感词。
综上所述，色情敏感词是一个需要谨慎对待的话题。我们应该自觉遵守法律法规和社会道德规范，不使用、不传播色情敏感词，共同维护网络环境的健康和秩序。交性，幼女，女幼，女==幼，性饺
    """
    res = sensitive_filter(content, 3)
    print(res)

def test_noise():
    fw = NoiseFilter()
    fw.add(["色情", "鸡吧", "小王八蛋"])
    msg = "我看你是小-鸡-吧。还是个@小@王@八@蛋"
    res = fw.scan(msg)
    print(res)

def test_scan():
    path = os.path.join(BASE_PATH, "data/load")
    filepath = os.path.join(path, "kws.txt")
    print(filepath)
    kws = open(filepath, "r", encoding="utf8").read().split("\n")
    print(kws)
    print("词数：", len(kws))

    sensitive_init(kws, [])
    print("初始化结束")

    content = "综上所述，鱼叉作为一种古老的捕鱼工具，在人类的渔猎历史中扮演着重要角色。然而，在现代社会，随着环保意识的提高和渔业法规的完善，鱼叉捕鱼的使用方式和合法性也受到了越来越多的关注和限制。"
    content = """
        色情敏感词是指那些与性、性行为、性器官或色情内容直接相关的词汇，这些词汇在特定语境下可能被视为不恰当、冒犯性或不适宜的。以下是对色情敏感词的详细解释：

    一、定义与分类
    定义：
    色情敏感词是指那些可能引发性暗示、性联想或描述性行为、性器官等内容的词汇。这些词汇在公共场合、正式场合或某些特定语境下使用可能被视为不恰当或冒犯性的。
    分类：
    直接描述性行为的词汇：如“性--交”、“做爱”等，这些词汇直接描述了性行为的过程。
    描述性器官的词汇：如“阴茎”、“阴道”等，这些词汇直接指代了人体的性器官。
    暗示性行为的词汇：如“挑逗”、“撩拨”等，这些词汇虽然不直接描述性行为，但可能引发性联想。
    色情网站或内容的词汇：如“色情网站”、“成人影片”等，这些词汇与色情内容直接相关。
    二、使用与传播的限制
    法律法规限制：
    在许多国家和地区，使用和传播色情敏感词可能违反法律法规。例如，在中国，传播淫秽物品是违法的，可能面临行政处罚或刑事处罚。
    社会道德规范：
    即使在没有明确法律限制的情况下，使用和传播色情敏感词也可能违反社会道德规范。这些行为可能被视为不道德、不尊重他人或破坏社会风气。
    网络平台监管：
    许多互联网平台都设有内容审核机制，用于屏蔽或过滤色情敏感词。这是为了保护用户的身心健康，维护网络环境的健康和秩序。
    三、影响与后果
    对个人形象的影响：
    在公共场合或正式场合使用色情敏感词可能损害个人形象，给人留下不专业、不礼貌或轻浮的印象。
    对社会风气的影响：
    广泛传播色情敏感词可能破坏社会风气，影响社会的道德观念和价值取向。
    法律后果：
    如前所述，使用和传播色情敏感词可能面临法律处罚，包括罚款、拘留甚至刑事责任。
    四、如何避免使用色情敏感词
    增强法律意识：
    了解并遵守相关法律法规，不传播、不制作色情内容。
    提高道德观念：
    尊重他人、尊重社会公德，不使用冒犯性或不适宜的词汇。
    注意语境：
    在特定语境下，即使某些词汇本身不是色情敏感词，也可能因语境的不同而引发性联想或误解。因此，在使用词汇时，要注意语境的适宜性。
    使用替代词汇：
    在需要描述性行为或性器官时，可以尝试使用更委婉、更中性的词汇来替代色情敏感词。
    综上所述，色情敏感词是一个需要谨慎对待的话题。我们应该自觉遵守法律法规和社会道德规范，不使用、不传播色情敏感词，共同维护网络环境的健康和秩序。交性，幼女，女幼，女==幼，性饺
        """
    res = sensitive_scan(content, 2)
    print("==", res)

def test_file_word():
    from src.config.sys_config import BASE_PATH
    dataload_path = os.path.join(BASE_PATH, "data/load")
    sensitive_path = os.path.join(dataload_path, "sensitive")
    # 遍历文件夹中的所有文件
    for filename in os.listdir(sensitive_path):
        file_path = os.path.join(sensitive_path, filename)

        # 确保是文件而不是文件夹
        if os.path.isfile(file_path):
            with open(file_path, 'r', encoding='utf-8') as file:
                # 读取文件内容并添加到敏感词列表
                for line in file.readlines():
                    words = line.strip()
                    if words in "几讥叽击刉饥圾机刏乩芨玑肌鸡矶奇其枅咭唧剞姬屐积畟笄飢基庴喞嵇嵆幾赍犄筓缉朞稘畸嗘跻鳮銈僟綨緁箕毄稽鹡觭躸諆緝畿嘰槣齑錤機禨墼激隮襀積璣磯羁賷簊擊櫅耭雞韲鶏譏譤鐖饑躋鷄鞿魕癪羇虀鑇鑙齏覉羈鸄覊亼亽及伋彶吉汲岌级忣极即亟郆佶卽姞叝急皍級笈革觙揤疾堲楖焏偮卙谻集殛戢棘極湒塉嵴楫辑愱蒺嫉蝍趌銡耤膌槉嶯潗濈蕺蕀瘠箿踑踖輯螏檝藉磼襋蹐鍓艥籍鏶霵轚鶺齎躤雧己丮妀犱纪泲虮济给挤脊掎鱾戟麂魢撠橶穖擠蟣彑计旡记伎齐系忌坖际芰妓技剂季迹垍既紀荠茤茍洎哜計記剤紒觊继绩悸梞旣祭偈徛寄寂済葪蔇惎臮跡鬾魝裚痵継蓟際兾勣漈漃禝穊稩暨魥鲚霁跽誋諅鲫暩稷穄曁冀劑薊髻覬薺嚌濟檕績罽繋璾檵蹟鵋鯽齌鯚懻癠穧骥蘎蘮瀱繼鱀鰶鰿鷑霽蘻鱭驥﨤樭撃鯲廭":
                        print(words)
                        print(file_path)
                        input("")

if __name__ == '__main__':
    # test_file_word()
    # test_file_set()

    # test_jieba()
    # test_hanziconv()
    # test_permutations()

    # test_sensitive()
    # test_noise()

    test_scan()


