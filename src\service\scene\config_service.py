import os
import json
import traceback
from src.config.constant import sceneFilePath, sceneListInfoFile
from src.config.constant import log


def sceneAdd(configJson):
    '''
        添加
    :param configJson: 模块配置
    :return: 新增或修改结果
    '''
    try:
        # 获取
        sceneId = configJson.get("scene_id", -1)
        name = configJson["name"]
        sceneIntention = configJson["scene_intention"]
        allScene = getScenceInfoDic()
        # 如果存在场景id
        if sceneId and sceneId != -1:
            # 如果场景id不存在
            if str(sceneId) not in allScene["scene"].keys():
                # 场景id置空
                sceneId = None
        else:
            # 场景id自增1
            sceneId = allScene["increment_id"] + 1
            # 自增id增加
            allScene["increment_id"] = sceneId
        # 如果场景id存在存储对应的配置
        if sceneId:
            # 存储列表文件
            allScene["scene"][str(sceneId)] = {
                "name": name,
                "scene_id": sceneId,
                "scene_intention": sceneIntention
            }
            json.dump(allScene, open(sceneListInfoFile, "w", encoding="utf8"), ensure_ascii=False, indent=4)
            # 存储场景配置
            json.dump(configJson, open(os.path.join(sceneFilePath, f"{sceneId}.json"), "w", encoding="utf8"),
                      ensure_ascii=False, indent=4)
            result = {"status": 200, "message": "ok", "data": name}
            log.info(f"【场景查询模块新增或修改成功】:{configJson}")
        else:
            result = {"status": 201, "message": "场景id不存在", "data": -1}
        log.info(f"【场景查询模块新增或修改结果】:{result}")
    except:
        result = {"status": 202, "message": "新增场景失败", "data": -1}
        log.error(f"【场景查询模块新增或修改失败】:{configJson}")
        log.error(traceback.format_exc())

    return result

def getScene(sceneId):
    '''
        根据ID获取场景的配置参数
    :param sceneId: 场景ID
    :return:
    '''
    sceneConfig = None
    try:
        if str(sceneId) in getScenceInfoDic()["scene"].keys():
            sceneConfig = json.load(open(os.path.join(sceneFilePath, f"{sceneId}.json"), "r", encoding="utf8"))
            log.info(f"【获取场景配置成功】:{sceneConfig}")
    except:
        log.error(f"【获取场景配置失败】: {sceneId}")
        log.error(traceback.format_exc())
    return sceneConfig

def sceneAll():
    '''
        格式化所有场景信息并返回
    :return:
    '''
    try:
        # 获取场景信息
        sceneListDic = getScenceInfoDic()["scene"]
        # 转为list
        sceneList = []
        for sceneId in sceneListDic:
            sceneList.append(sceneListDic[sceneId])
        result = {
            "code": 200,
            "msg": "success",
            "data": {
                "total": len(sceneList),
                "list": sceneList
            }
        }
        log.info(f"【查询所有场景成功】: {result}")
    except:
        result = {"status": 202, "msg": "获取所有场景失败", "data": {}}
        log.error("【查询所有场景失败】")
        log.error(traceback.format_exc())
    return result


def getScenceInfoDic():
    '''
        获取已有场景信息
    :return: 以场景id为key的字典
    '''
    # 读取场景文件并返回
    return json.load(open(sceneListInfoFile, "r", encoding="utf8"))
