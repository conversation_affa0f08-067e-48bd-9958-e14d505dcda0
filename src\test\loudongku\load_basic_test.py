import os
import json
import requests
from tqdm import tqdm


def upload_json_data(json_file_path, url, kbase_id, model):
    """
    从单个JSON文件读取字典数据并上传到指定接口，支持断点续传和显示进度
    :param json_file_path: JSON文件路径
    :param url: 上传接口的URL
    :param kbase_id: 知识库ID
    :param model: 分段形式（chapter/length）
    :return: 无
    """
    # 加载JSON数据
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
    except Exception as e:
        print(f"加载JSON文件失败: {e}")
        return

    # 检查JSON数据是否为字典
    if not isinstance(json_data, dict):
        print("JSON数据应该是一个字典")
        return

    # 检查已上传的条目，避免重复上传
    uploaded_items = set()
    if os.path.exists("uploaded_items.txt"):
        with open("uploaded_items.txt", "r", encoding='utf-8') as f:
            uploaded_items = set(f.read().splitlines())

    # 使用tqdm显示进度条
    with tqdm(total=len(json_data), desc="上传进度") as pbar:
        for title, content in json_data.items():
            # 使用title作为唯一标识
            item_id = title

            # 如果条目已经上传过，则跳过
            if item_id in uploaded_items:
                pbar.update(1)
                continue

            try:
                # 构建上传数据
                item_data = {
                    "title": title,
                    "content": content
                }
                item_json = json.dumps(item_data, ensure_ascii=False)

                # 准备文件上传
                files = {
                    'file': (f'{title}.json', item_json, 'application/json')
                }

                headers = {
                    'Accept': '*/*',
                    'Connection': 'keep-alive',
                }

                mapping = '''
                {"title": "title","book_name": {"source": "filename"},"summary": "","content": "content"}
                '''

                # 发送POST请求
                response = requests.post(
                    url,
                    headers=headers,
                    files=files,
                    params={
                        "kbase_id": kbase_id,
                        "model": model,
                        "mapping": mapping
                    }
                )

                # 检查响应状态
                if response.status_code == 200:
                    # 如果上传成功，记录条目title
                    with open("uploaded_items.txt", "a", encoding='utf-8') as f:
                        f.write(item_id + "\n")
                    pbar.update(1)
                else:
                    print(f"条目 '{title}' 上传失败，状态码：{response.status_code}")
                    print(response.text)

            except Exception as e:
                print(f"条目 '{title}' 上传出错：{e}")
    print("所有数据上传完成")


# 示例用法
if __name__ == "__main__":
    json_file_path = r"/root/rag_bachang/service/api/rag/doc/网络安全知识.json"
    # json_file_path = r"E:\program\python\rag_devc\doc\网络安全知识.json"
    url = "http://localhost:9252/file/upload"
    kbase_id = "8427642996"
    model = "auto"

    upload_json_data(json_file_path, url, kbase_id, model)