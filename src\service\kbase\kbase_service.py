import os
import json
import time
import shutil
import traceback
from symbol import parameters

from src.config.constant import log, kbaseFilePath
from src.utils.id_util import genNoRepeatNum10Id
import src.service.firewall.document_service as documentService
from src.utils.elasticsearch_util import elasticSearchEngine, get_now_time
from src.config.constant import max_doc_num, es_max_kbase_num
from src.config.constant import es_doc_index, es_kbase_index, milvus_index


def initKbase():
    kbaseMapping = {
        "mappings": {
            "properties": {
                "name": {  # 知识库名
                    "type": "text",
                    "analyzer": "ik_max_word"
                },
                "name_keyword": {  # 知识库ID
                    "type": "keyword"
                },
                "kbase_id": {  # 知识库ID
                    "type": "keyword"
                },
                "create_time": {  # 创建时间
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "update_time": {  # 更新时间
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                }
            }
        }
    }
    try:
        elasticSearchEngine.create(index_name=es_kbase_index, mapping=kbaseMapping)
    except:
        log.error("【知识库索引创建出错】")
        log.error(traceback.format_exc())


initKbase()

def getKbaseNum():
    '''
        查询知识库数量
    :return: 数量
    '''
    query = {
        "query": {
            "match_all": {}
        }
    }
    result = elasticSearchEngine.search(query=query, index_name=es_kbase_index)
    log.info(f"【知识库数量查询结果】:{result}")
    return result["hits"]["total"]["value"]

def createKbase(kbaseName, kbaseDesc):
    '''
        创建新的知识库
    :param kbaseName: 知识库名称
    :return: 知识库创建结果
    '''
    log.info(f"【知识库创建参数】:{kbaseName}: {kbaseDesc}")
    try:
        if getKbaseNum() >= es_max_kbase_num:
            createKbaseResult = {"status": 202, "message": "超过知识库最大数量", "data": kbaseName}
            log.info(f"【知识库创建结果】:{createKbaseResult}")
            return json.dumps(createKbaseResult, ensure_ascii=False)
        # 判断知识库名称是否重复
        if kbaseName.strip() == "" or elasticSearchEngine.find_filed(field="name_keyword", field_value=kbaseName,
                                                                     index_name=es_kbase_index):
            createKbaseResult = {"status": 202, "message": "知识库名称已存在", "data": kbaseName}
        else:
            # 生成不与库中id重复的新id
            kbaseId = 0
            for genNum in range(10):
                kbaseId = genNoRepeatNum10Id()
                if not elasticSearchEngine.find_filed(field="kbase_id", field_value=kbaseId, index_name=es_kbase_index):
                    break
                else:
                    kbaseId = 0
            if kbaseId != 0:
                currentTime = get_now_time()
                kbaseInfo = {
                    "name": kbaseName,
                    "desc": kbaseDesc,
                    "name_keyword": kbaseName,
                    "kbase_id": kbaseId,
                    "create_time": currentTime,
                    "update_time": currentTime
                }
                elasticSearchEngine.insert(document=kbaseInfo, index_name=es_kbase_index)
                log.info(f"【知识库创建入库成功】:{kbaseId}")
                currentKbaseFilePath = os.path.join(kbaseFilePath, str(kbaseId))
                if not os.path.exists(currentKbaseFilePath):
                    os.mkdir(currentKbaseFilePath)
                log.info(f"【知识库创建文件夹成功】:{currentKbaseFilePath}")
                createKbaseResult = {
                    "status": 200,
                    "message": "知识库创建成功",
                    "data": {
                        "kbase_id": kbaseId,
                        "kbase_name": kbaseName
                    }
                }
                log.info(f"【知识库创建成功】:{kbaseName}")
                ## 防止因创建知识库后直接上传文件造成的知识库不存在
                documentService.es_data_refresh_manually(es_kbase_index)
            else:
                log.error(f"【知识库不重复id生成失败】:{kbaseName}")
                createKbaseResult = {"status": 202, "message": "出错啦，请重试", "data": kbaseName}
    except:
        log.error(f"【知识库创建失败】:{kbaseName}")
        log.error(traceback.format_exc())
        createKbaseResult = {"status": 202, "message": "出错啦，请重试", "data": kbaseName}
    log.info(f"【知识库创建结果】:{createKbaseResult}")
    return json.dumps(createKbaseResult, ensure_ascii=False)


def updateKbase(kbaseId, kbaseName, kbaseDesc):
    '''
        修改知识库信息
    :param kbaseId: 知识库id
    :param kbaseName: 知识库名称
    :return: 修改知识库结果
    '''
    parameters = {
        "kbaseId": kbaseId
    }
    if kbaseName:
        parameters['kbaseName'] = kbaseName
    if kbaseDesc:
        parameters['kbaseDesc'] = kbaseDesc

    log.info(f"【知识库修改参数】:{parameters}")
    try:
        # 判断知识库名称是否重复
        # if kbaseName.strip() == "" or elasticSearchEngine.find_filed(field="name_keyword", field_value=kbaseName,
        #                                                              index_name=es_kbase_index):
        #     updateKbaseResult = {"status": 202, "message": "知识库名称已存在", "data": kbaseName}
        if kbaseId == 0 or not elasticSearchEngine.find_filed(field="kbase_id", field_value=kbaseId,
                                                                index_name=es_kbase_index):
            updateKbaseResult = {"status": 202, "message": "知识库id不存在", "data": kbaseName}
        else:
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "term": {
                                    "kbase_id": kbaseId
                                }
                            }
                        ]
                    }
                }
            }
            kbaseInfo = elasticSearchEngine.search(query=query, index_name=es_kbase_index)
            if kbaseInfo["hits"]["total"]["value"] > 0:

                source_ = kbaseInfo["hits"]["hits"][0]["_source"]
                old_kbase_name = ""
                if "name" in source_:
                    old_kbase_name = source_["name"]
                old_kbase_desc = ""
                if "desc" in source_:
                    old_kbase_desc = source_["desc"]

                if not parameters.get('kbaseName') and not old_kbase_name:
                    updateKbaseResult = {"status": 202, "message": "知识库名称更新失败", "data": kbaseName}
                else:
                    document = {
                        "doc": {
                            "name": parameters.get('kbaseName', old_kbase_name),
                            "desc": parameters.get('kbaseDesc', old_kbase_desc),
                            "name_keyword": parameters.get('kbaseName', old_kbase_name),
                            "update_time": get_now_time()
                        }
                    }
                    elasticSearchEngine.update(document_id=kbaseInfo["hits"]["hits"][0]["_id"],
                                               document=document,
                                               index_name=es_kbase_index)
                    updateKbaseResult = {"status": 200, "message": "更新成功", "data": kbaseName}
            else:
                updateKbaseResult = {"status": 202, "message": "id不存在", "data": kbaseName}
    except:
        log.error(f"【知识库修改失败】:{parameters}")
        log.error(traceback.format_exc())
        updateKbaseResult = {"status": 202, "message": "出错啦，请重试", "data": kbaseName}
    log.info(f"【知识库修改结果】:{updateKbaseResult}")
    return json.dumps(updateKbaseResult, ensure_ascii=False)


def getKbaseDocsNum(kbaseId):
    '''
        查询知识库的当前分段数量
    :param kbaseId: 知识库id
    :return: 分段的数量
    '''
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "kbase_id": kbaseId
                        }
                    }
                ]
            }
        },
        "collapse": {
            "field": "doc_id"
        },
        "from": 0,
        "track_total_hits": True,
        "aggs": {
            "courseAgg": {
                "cardinality": {
                    "field": "doc_id"
                }
            }
        }
    }
    listInfo = elasticSearchEngine.search(query=query, index_name=es_doc_index)
    return listInfo["aggregations"]["courseAgg"]["value"]


def listKbase():
    '''
        查询所有知识库
    :return: 知识库列表
    '''
    log.info("【知识库列表查询参数】")
    try:
        query = {
            "size": 999,
            "query": {
                "match_all": {}
            }
        }
        listInfo = elasticSearchEngine.search(query=query, index_name=es_kbase_index)
        kbaseList = []
        for oneKbase in listInfo["hits"]["hits"]:
            kbaseList.append({
                "kbaseName": oneKbase["_source"]["name"],
                "kbaseId": oneKbase["_source"]["kbase_id"],
                "currentDocs": getKbaseDocsNum(oneKbase["_source"]["kbase_id"]),
                "desc": oneKbase["_source"].get("desc", oneKbase["_source"]["name"]),
                "maxDocs": max_doc_num,
                "createTime": oneKbase["_source"]["create_time"],
                "updateTime": oneKbase["_source"]["update_time"],
            })
        listKbaseResult = {
            "status": 200,
            "message": "成功",
            "data": kbaseList
        }
    except:
        log.error(f"【知识库列表查询失败】")
        log.error(traceback.format_exc())
        listKbaseResult = {"status": 202, "message": "出错啦，请重试", "data": ""}
    log.info(f"【知识库列表结果】:{listKbaseResult}")
    return json.dumps(listKbaseResult, ensure_ascii=False)


def deleteKbase(kbaseId):
    '''
        删除知识库
    :param kbaseId: 知识库id
    :return: 删除知识库结果
    '''
    log.info(f"【知识库删除参数】:{kbaseId}")
    try:
        # 判断知识库id是否存在
        if kbaseId == 0 or not elasticSearchEngine.find_filed(field="kbase_id", field_value=kbaseId,
                                                              index_name=es_kbase_index):
            deleteKbaseResult = {"status": 202, "message": "知识库id不存在", "data": kbaseId}
        else:
            documentService.milvus_delete_by_field(field="kbase_id", field_value=kbaseId, collection_name=milvus_index)
            elasticSearchEngine.delete_by_kbase_id(kbase_id=kbaseId, index_name=es_doc_index)
            elasticSearchEngine.delete_by_kbase_id(kbase_id=kbaseId, index_name=es_kbase_index)
            currentKbaseFilePath = os.path.join(kbaseFilePath, str(kbaseId))
            if os.path.exists(currentKbaseFilePath):
                shutil.rmtree(currentKbaseFilePath, ignore_errors=True)
            deleteKbaseResult = {"status": 200, "message": "删除成功", "data": kbaseId}
    except:
        log.error(f"【知识库删除失败】")
        log.error(traceback.format_exc())
        deleteKbaseResult = {"status": 202, "message": "出错啦，请重试", "data": ""}
    log.info(f"【知识库删除结果】:{deleteKbaseResult}")
    return json.dumps(deleteKbaseResult, ensure_ascii=False)

def restoreFactorySettings(secret):
    '''
        回复出厂设置
    :param secret: 恢复出厂设置密钥
    :return:
    '''
    log.info(f"【恢复出厂设置参数】:{secret}")
    try:
        if secret != "8f7c4e67001dd5439a21503dc836c4b6":
            restpreKbaseResult = {"status": 202, "message": "参数错误", "data": ""}
        else:
            kbaseList = json.loads(listKbase())
            for kbaseOne in kbaseList["data"]:
                log.info(f"【删除知识库】:{kbaseOne['kbaseId']} _ {kbaseOne['kbaseName']}")
                deleteKbase(kbaseOne['kbaseId'])
            log.info(f"【恢复出厂设置成功】")
            restpreKbaseResult = {"status": 200, "message": "恢复出厂设置成功", "data": ""}
    except:
        log.error(f"【恢复出厂设置失败】")
        log.error(traceback.format_exc())
        restpreKbaseResult = {"status": 202, "message": "恢复出厂设置失败", "data": ""}
    log.info(f"【恢复出厂设置结果】:{restpreKbaseResult}")
    return json.dumps(restpreKbaseResult, ensure_ascii=False)