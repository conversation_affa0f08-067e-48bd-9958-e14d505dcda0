import json

def isAddQuestion(currentHead, textList, headList, themeTemp, themeSentenceTemp, sentenceTemp):
    '''
    解析当前内容，判断是否添加到问题列表中
    :param currentHead: 当前对应的内容信息
    :param textList: 内容解析总缓存
    :param headList: 当前内容所在标题树
    :param themeTemp: 主题缓存
    :param themeSentenceTemp: (段落前的总结性语句)主题  所包含的句子的缓存
            安装：
            安装xxxx需要xxxx
    :param sentenceTemp: (带有章节信息的)标题  所包含的该章节所有句子缓存
            1.2.2.5 xxxxx
            xxxxxxxx
    :return:
    '''
    # 如果类型是文本
    if currentHead["type"] == "text":
        # 保留文本内容信息到标题所对应的缓存中
        sentenceTemp.append({"type": "text", "content": currentHead["value"]})
        # 保留文本内容信息到主体对应的缓存中
        themeSentenceTemp.append({"type": "text", "content": currentHead["value"]})

    # 如果类型是表格
    if currentHead["type"] == "table":
        # 将表格内容添加到标题内容缓存和主题对应的内容缓存中
        sentenceTemp.append({"type": "table", "content": currentHead["value"]})
        themeSentenceTemp.append({"type": "table", "content": currentHead["value"]})

    if currentHead["type"] == "image":
        # 将图片信息添加到标题内容缓存和主体内容缓存中
        sentenceTemp.append({"type": "image", "content": currentHead["value"]})
        themeSentenceTemp.append({"type": "image", "content": currentHead["value"]})

    # 如果类型是主题
    if currentHead["type"] == "theme":
        # 如果主题和主题对应的内容缓存都不存在
        if len(themeSentenceTemp) != 0 and themeTemp.strip() != "":
            # 将标题树，主题，主题锁包含的内容存储
            textList.append([headList.copy(), themeTemp, themeSentenceTemp])
        # 重置主题
        themeTemp = currentHead["value"]
        # 重置主题对应的内容
        themeSentenceTemp = []

    return textList, themeTemp, themeSentenceTemp, sentenceTemp


def endForHead(textList, headList, themeTemp, themeSentenceTemp, sentenceTemp):
    '''
    结束当前标题所对应的内容
    :param textList: 总内容缓存
    :param headList: 标题树
    :param themeTemp: 主题缓存
    :param themeSentenceTemp: 主题所对应的内容缓存
    :param sentenceTemp: 标题所对应的内容缓存
    :return:
    '''
    # 如果主题和主题所对应的内容都不为空
    if len(themeSentenceTemp) != 0 and themeTemp.strip() != "":
        # 存储该主题和主题所对应的内容
        textList.append(
            [headList.copy(), themeTemp,
             themeSentenceTemp])
    # 如果标题所对应的内容不为空
    if len(sentenceTemp) != 0:
        # 存储该标题和对应的内容
        title_sp = headList.copy()[-1].split(' ')
        title = ''.join(title_sp[3:])
        textList.append(
            [headList.copy(), title,
             sentenceTemp])
    # 重置缓存
    sentenceTemp = []
    themeTemp = ""
    themeSentenceTemp = []
    return textList, themeTemp, themeSentenceTemp, sentenceTemp


# 格式化好后的文章所有信息
# [headList(list) , theme(str) , text(list)]


# sentenceTemp = []
# themeTemp = ""
# themeSentenceTemp = []
#
# # 存储当前标题
# currentHeadList = []
# # 最大标题对应的序号
# realFirstHeadDic = {}


def analyzeAllText(allTextList: list, realFirstHeadDic: dict, currentHeadList: list, textList: list, themeTemp: str,
                   themeSentenceTemp: list, sentenceTemp: list):
    for head in allTextList:
        if len(currentHeadList) < 100:
            if head["type"] == "section":
                # 保留大标题对应的序号
                if head["title"].startswith("Heading 1"):
                    realFirstHeadDic[head["title"]] = len(realFirstHeadDic) + 1
                # 存储当前标题
                currentHeadList.append(head["title"])
                # 递归遍历下级标题内容
                analyzeAllText(head["content"], realFirstHeadDic, currentHeadList, textList, themeTemp,
                               themeSentenceTemp, sentenceTemp)
            else:
                # 保存标题对应内容中的所有信息
                textList, themeTemp, themeSentenceTemp, sentenceTemp = isAddQuestion(head, textList, currentHeadList,
                                                                                     themeTemp, themeSentenceTemp,
                                                                                     sentenceTemp)

            if head["type"] == "section":
                # 保存当前标题对应的所有内容
                textList, themeTemp, themeSentenceTemp, sentenceTemp = endForHead(textList, currentHeadList, themeTemp,
                                                                                  themeSentenceTemp, sentenceTemp)
                currentHeadList.pop()


from datetime import datetime


def prase_format(textList, book_name, book_id):
    prase_data = []
    for index, line in enumerate(textList):
        content = ''
        for item in line[2]:
            if isinstance(item['content'], list):
                tabel = ''
                for row in item['content']:
                    if '|' in row and not all(c == '|' for c in row):
                        tabel += row
                        tabel += '\n'
                content += tabel
            elif isinstance(item['content'], str):
                content += item['content']

        line_data = {
            "title": "||".join(line[0]),  # + "||" + line[1]
            "book_name": book_name,
            "summary": "",
            "content": content,
        }

        # prase_data.append({"index": {"_index": "dev_qgpt_cn_search", "_type": "_doc"}})
        prase_data.append(line_data)

    return prase_data


import os
import hashlib
import uuid


def generate_combined_id(book_title):
    md5 = hashlib.md5()
    md5.update(book_title.encode('utf-8'))
    base_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, md5.hexdigest())
    return str(base_uuid)


# def mainnn():
#     textList = []
#     base_path = r"E:\program\python\qgpt\doc\625"
#     parsed_path = base_path + r"\parsed_data"
#     for root, dirs, files in os.walk(parsed_path):
#         for file in files:
#             extension_name = os.path.splitext(file)[1]
#             if extension_name != ".json":
#                 continue
#             file_name = os.path.splitext(file)[0]
#             print(file_name)
#             if not os.path.exists(os.path.join(base_path, file_name + '.docx')):
#                 print(f'{file_name}.docx not exist, error')
#             docx_json_file = os.path.join(root, file)
#             allTextList = json.load(open(docx_json_file, "r", encoding="utf8"))
#             analyzeAllText(allTextList, realFirstHeadDic, currentHeadList, textList, themeTemp, themeSentenceTemp,
#                            sentenceTemp)
#
#             output_file = os.path.join(os.path.join(base_path, 'concate_data'), '%s.json' % file_name)
#             prase = prase_format(textList, file_name + '.docx', generate_combined_id(file_name))
#             json.dump(prase, open(output_file, "w", encoding="utf8"), ensure_ascii=False)


def concate(json_parsed, textList=[],
            sentenceTemp=[],
            themeTemp="",
            themeSentenceTemp=[],
            currentHeadList=[],
            realFirstHeadDic={},
            file_name=""):
    analyzeAllText(json_parsed, realFirstHeadDic, currentHeadList, textList, themeTemp, themeSentenceTemp,
                   sentenceTemp)
    prase = prase_format(textList, file_name, generate_combined_id(file_name))

    return prase


if __name__ == '__main__':
    # mainnn()
    print(11111111111111)
    assert 1 == 1
