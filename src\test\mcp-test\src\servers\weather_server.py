import json
import httpx
import requests
import sys
import os
from typing import Any
from mcp.server.fastmcp import FastMCP
from fake_useragent import UserAgent

# 确保输出使用 UTF-8 编码
if sys.stdout.encoding != 'utf-8':
    # 在 Windows 上强制使用 UTF-8
    if os.name == 'nt':
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 导入共享日志配置
from logger_config import get_logger

# 获取日志记录器
logger = get_logger("weather_server")

# 初始化 MCP 服务器
logger.info("初始化 MCP 服务器")
mcp = FastMCP("WeatherServer")

# OpenWeather API 配置
OPENWEATHER_API_BASE = "https://api.openweathermap.org/data/2.5/weather"
API_KEY = "********************************"  # 请替换为你自己的 OpenWeather API Key
USER_AGENT = "weather-app/1.0"

# 初始化UserAgent
ua = UserAgent()

async def fetch_weather(city: str) -> dict[str, Any] | None:
    """
    从 OpenWeather API 获取天气信息。
    :param city: 城市名称（需使用英文，如 Beijing）
    :return: 天气数据字典；若出错返回包含 error 信息的字典
    """
    logger.info(f"获取天气信息: {city}")
    params = {
        "q": city,
        "appid": API_KEY,
        "units": "metric",
        "lang": "zh_cn"
    }
    logger.debug(f"请求参数: {params}")
    
    # 使用随机 User-Agent
    headers = {"User-Agent": ua.random}
    logger.debug(f"请求头: {headers}")

    async with httpx.AsyncClient() as client:
        try:
            logger.info(f"发送请求到 OpenWeather API")
            response = await client.get(OPENWEATHER_API_BASE, params=params, headers=headers, timeout=30.0)
            logger.debug(f"API 响应状态码: {response.status_code}")
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"成功获取 {city} 的天气信息")
            return data
        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP 错误: {e.response.status_code}"
            logger.error(error_msg, exc_info=True)
            return {"error": error_msg}
        except httpx.ReadTimeout as e:
            error_msg = f"请求超时: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"error": error_msg}
        except httpx.ConnectError as e:
            error_msg = f"连接错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {"error": error_msg}

def format_weather(data: dict[str, Any] | str) -> str:
    """
    将天气数据格式化为易读文本。
    :param data: 天气数据（可以是字典或 JSON 字符串）
    :return: 格式化后的天气信息字符串
    """
    logger.info("格式化天气数据")
    # 如果传入的是字符串，则先转换为字典
    if isinstance(data, str):
        try:
            data = json.loads(data)
        except Exception as e:
            logger.error(f"无法解析天气数据: {e}", exc_info=True)
            return f"无法解析天气数据: {e}"

    # 如果数据中包含错误信息，直接返回错误提示
    if "error" in data:
        logger.warning(f"天气数据包含错误: {data['error']}")
        return f"⚠️ {data['error']}"

    # 提取数据时做容错处理
    city = data.get("name", "未知")
    country = data.get("sys", {}).get("country", "未知")
    temp = data.get("main", {}).get("temp", "N/A")
    humidity = data.get("main", {}).get("humidity", "N/A")
    wind_speed = data.get("wind", {}).get("speed", "N/A")
    # weather 可能为空列表，因此用 [0] 前先提供默认字典
    weather_list = data.get("weather", [{}])
    description = weather_list[0].get("description", "未知")

    formatted_result = (
        f"🌍 {city}, {country}\n"
        f"🌡 温度: {temp}°C\n"
        f"💧 湿度: {humidity}%\n"
        f"🌬 风速: {wind_speed} m/s\n"
        f"🌤 天气: {description}\n"
    )
    
    logger.info(f"天气数据格式化完成: {city}, {country}")
    return formatted_result

async def fetch_cve(cve_id: str) -> dict[str, Any] | None:
    """
    从 漏洞库 API 获取漏洞信息。
    :param cve: cve的id编号（如 CVE-2021-12345）
    :return: 漏洞数据字典；若出错返回包含 error 信息的字典
    """
    logger.info(f"获取CVE信息: {cve_id}")
    api_url_template = "https://stack.chaitin.com/api/v2/vuln/list/"  # 添加末尾斜杠

    params = {
            "offset": 0,
            "search": cve_id
        }
    logger.debug(f"请求参数: {params}")

    # 请求头
    headers = {
        'User-Agent': ua.random,  # 使用随机User-Agent
        'Accept': '*/*',
        'Host': 'stack.chaitin.com',
        'Connection': 'keep-alive',
        'Cookie': 'sl-session=Gw4GCTYpI2hWurQKFIh2ww=='
    }

    async with httpx.AsyncClient(follow_redirects=True) as client:  # 启用自动跟随重定向
        try:
            logger.info(f"发送请求到漏洞库 API")
            response = await client.get(api_url_template, params=params, headers=headers, timeout=30.0)
            logger.debug(f"API 响应状态码: {response.status_code}")
            
            # 打印完整的请求URL和响应头，帮助调试
            logger.debug(f"请求URL: {response.request.url}")
            logger.debug(f"响应头: {response.headers}")
            
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"成功获取 {cve_id} 的漏洞信息")
            return data
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP 错误: {e.response.status_code}", exc_info=True)
            # 尝试读取响应内容，可能包含错误信息
            error_content = ""
            try:
                error_content = e.response.text
            except:
                pass
            logger.error(f"错误响应内容: {error_content}")
            return {"error": f"HTTP 错误: {e.response.status_code}"}
        except Exception as e:
            logger.error(f"请求失败: {str(e)}", exc_info=True)
            return {"error": f"请求失败: {str(e)}"}
        
def format_cve(data: dict[str, Any] | str) -> str:
    """
    将cve漏洞数据格式化为易读文本。
    :param data: 漏洞数据（可以是字典或 JSON 字符串）
    :return: 格式化后的漏洞信息字符串
    """
    logger.info("格式化漏洞数据")
    # 如果传入的是字符串，则先转换为字典
    if isinstance(data, str):
        try:
            data = json.loads(data)
        except Exception as e:
            logger.error(f"无法解析漏洞数据: {e}", exc_info=True)
            return f"无法解析漏洞数据: {e}"
    
    # 如果数据中包含错误信息，直接返回错误提示
    if data.get("msg") == "success" and data.get("data") and data["data"].get("list"):
        vulnerabilities = data["data"]["list"]
        for vuln in vulnerabilities:
            useful_data = [
                vuln.get("id"),
                vuln.get("title"),
                vuln.get("summary"),
                vuln.get("weakness"),
                vuln.get("severity"),
                vuln.get("cve_id"),
                vuln.get("cnvd_id"),
                vuln.get("cnnvd_id"),
                vuln.get("impact"),
                vuln.get("fix_steps"),
                vuln.get("disclosure_date"),
                vuln.get("poc_disclosure_date"),
                vuln.get("patch_date"),
                vuln.get("references")
            ]
            
            formatted_result = (
                f"漏洞编号: {useful_data[0]}\n"
                f"漏洞名称: {useful_data[1]}\n"
                f"漏洞简介: {useful_data[2]}\n"
                f"漏洞危害: {useful_data[3]}\n"
                f"漏洞等级: {useful_data[4]}\n"
                f"漏洞CVE编号: {useful_data[5]}\n"
                f"漏洞CNVD编号: {useful_data[6]}\n"
                f"漏洞CNNVD编号: {useful_data[7]}\n"
                f"漏洞影响范围: {useful_data[8]}\n"
                f"漏洞修复建议: {useful_data[9]}\n"
                f"漏洞披露时间: {useful_data[10]}\n"
            )
            
            logger.info(f"漏洞数据格式化完成: {useful_data[5]}")
            return formatted_result
    else:
        logger.warning(f"未找到CVE编号的漏洞数据")
        return(f"未找到CVE编号的漏洞数据。")

@mcp.tool()
async def query_weather(city: str) -> str:
    """
    输入指定城市的英文名称，返回今日天气查询结果。
    :param city: 城市名称（需使用英文）
    :return: 格式化后的天气信息
    """
    logger.info(f"调用天气查询工具: {city}")
    data = await fetch_weather(city)
    result = format_weather(data)
    logger.info(f"天气查询完成: {city}")
    return result


@mcp.tool()
async def query_cve(cve_id: str) -> str:
    """
    输入指定cve的id编号, 返回漏洞查询结果。
    :param cve_id: 漏洞编号
    :return: 格式化后的漏洞信息
    """
    logger.info(f"调用漏洞查询工具: {cve_id}")
    data = await fetch_cve(cve_id)
    result = format_cve(data)
    logger.info(f"漏洞查询完成: {cve_id}")
    return result

if __name__ == "__main__":
    # 检查命令行参数，如果有测试参数则运行测试
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        import asyncio
        
        async def test_cve_query():
            """测试 CVE 查询功能"""
            logger.info("开始测试 CVE 查询功能")
            
            # 测试一个知名的 CVE 编号
            cve_id = "CVE-2021-44228"  # Log4Shell 漏洞
            if len(sys.argv) > 2:
                cve_id = sys.argv[2]  # 使用命令行提供的 CVE 编号
                
            logger.info(f"测试查询 CVE: {cve_id}")
            
            try:
                # 调用 fetch_cve 函数获取漏洞数据
                data = await fetch_cve(cve_id)
                logger.info(f"获取到原始数据: {json.dumps(data, ensure_ascii=False)[:200]}...")
                
                # 格式化漏洞数据
                result = format_cve(data)
                logger.info("格式化结果:")
                print("\n" + "="*50)
                print(f"CVE 查询结果 ({cve_id}):")
                print(result)
                print("="*50 + "\n")
                
                return result
            except Exception as e:
                logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
                print(f"测试过程中出错: {str(e)}")
                return None
        
        # 运行测试
        result = asyncio.run(test_cve_query())
        sys.exit(0)  # 测试完成后退出
    
    # 如果没有测试参数，则以标准 I/O 方式运行 MCP 服务器
    logger.info("以标准 I/O 方式启动 MCP 服务器")
    mcp.run(transport='stdio')
