import datetime

"""peewee提供了一个数据库的封装模型，playhouse.db_url为其连接的一种方式通过数据库的指定格式url连接
连接后创建完以后需要模型生成表使用db.connect()，db.create_tables([Person, Pet])
"""


from datetime import datetime
import uuid
from functools import wraps
from typing import Optional

from peewee import *
from playhouse.pool import PooledMySQLDatabase
import pymysql
# 注册 PyMySQL 作为 MySQLdb
pymysql.install_as_MySQLdb()
# 数据库配置
DB_CONFIG = {
    'database': 'btime_cloud_test',
    'user': 'pub_xs_ms',
    'password': 'fc93f9ccf9a0065f8',
    'host': '************',
    'port': 3306,
    'max_connections': 20,
    'stale_timeout': 300
}

# 创建数据库连接池
DB = PooledMySQLDatabase(**DB_CONFIG)


# 基础模型类
class BaseModel(Model):
    id = CharField(primary_key=True, max_length=36)
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField(default=datetime.now)

    class Meta:
        database = DB

    def save(self, *args, **kwargs):
        self.updated_at = datetime.now()
        return super().save(*args, **kwargs)


# 用户模型
class User(BaseModel):
    username = CharField(max_length=64, unique=True, index=True)  # 减小长度并添加索引
    email = CharField(max_length=128, unique=True, index=True)  # 减小长度并添加索引
    password = CharField(max_length=128)  # 减小长度
    is_active = BooleanField(default=True)

    class Meta:
        table_name = 'users'
        indexes = (
            # 复合索引示例（如果需要的话）
            (('username', 'email'), False),  # False 表示不是唯一索引
        )


def init_database():
    """初始化数据库表"""
    # 首先需要确保数据库使用正确的字符集
    DB.execute_sql("""
        ALTER DATABASE btime_cloud_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    """)

    # 创建表
    with DB:
        DB.create_tables([User], safe=True)


def main():
    try:
        # 初始化数据库
        init_database()

        # 创建用户
        user = UserService.create_user(
            username="john_doe",
            email="<EMAIL>",
            password="secure_password"
        )
        print(f"Created user: {user.username}")

        # 查询用户
        found_user = UserService.get_user_by_email("<EMAIL>")
        if found_user:
            print(f"Found user: {found_user.username}")

        # 更新用户
        UserService.update_user(
            user_id=user.id,
            username="john_doe_updated"
        )
        print("Updated user")

        # 列出所有用户
        users = UserService.list_users()
        for u in users:
            print(f"Listed user: {u.username}")

        # 删除用户
        UserService.delete_user(user.id)
        print("Deleted user")

    except Exception as e:
        print(f"Error occurred: {e}")
    finally:
        DB.close()


# UserService 类保持不变...
class UserService:
    model = User

    @classmethod
    @DB.connection_context()
    def create_user(cls, username: str, email: str, password: str) -> User:
        return cls.model.create(
            id=str(uuid.uuid4()),
            username=username,
            email=email,
            password=password
        )

    @classmethod
    @DB.connection_context()
    def get_user_by_email(cls, email: str) -> Optional[User]:
        return cls.model.get_or_none(cls.model.email == email)

    @classmethod
    @DB.connection_context()
    def update_user(cls, user_id: str, **kwargs) -> int:
        return cls.model.update(**kwargs) \
            .where(cls.model.id == user_id) \
            .execute()

    @classmethod
    @DB.connection_context()
    def delete_user(cls, user_id: str) -> int:
        return cls.model.delete().where(cls.model.id == user_id).execute()

    @classmethod
    @DB.connection_context()
    def list_users(cls, limit: int = 10, offset: int = 0) -> list[User]:
        return list(cls.model.select().limit(limit).offset(offset))


if __name__ == "__main__":
    main()