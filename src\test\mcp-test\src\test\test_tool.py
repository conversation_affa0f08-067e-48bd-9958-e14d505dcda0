import sys
import os
import asyncio
import json
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from contextlib import AsyncExitStack
import os
import sys

# 导入共享日志配置
from logger_config import get_logger

# 获取日志记录器
logger = get_logger("test_tool")

async def test_weather_tool():
    """测试天气工具调用"""
    logger.info("开始测试天气工具")
    
    exit_stack = AsyncExitStack()
    
    try:
        # 设置环境变量，强制使用 UTF-8 编码
        env = os.environ.copy()
        env["PYTHONIOENCODING"] = "utf-8"
        
        server_params = StdioServerParameters(
            command="python",
            args=["weather_server.py"],
            env=env
        )
        
        # 启动 MCP 服务器并建立通信
        logger.info("启动 MCP 服务器并建立通信")
        stdio_transport = await exit_stack.enter_async_context(stdio_client(server_params))
        stdio, write = stdio_transport
        session = await exit_stack.enter_async_context(ClientSession(stdio, write))
        
        await session.initialize()
        
        # 列出 MCP 服务器上的工具
        response = await session.list_tools()
        tools = response.tools
        tool_names = [tool.name for tool in tools]
        logger.info(f"可用工具: {tool_names}")
        
        # 打印每个工具的详细信息
        for tool in tools:
            logger.info(f"工具名称: {tool.name}")
            logger.info(f"工具描述: {tool.description}")
            logger.info(f"工具输入模式: {tool.inputSchema}")
        
        # 直接调用天气工具
        logger.info("直接调用天气工具")
        try:
            result = await session.call_tool("query_weather", {"city": "Beijing"})
            
            logger.info(f"工具调用结果类型: {type(result)}")
            logger.info(f"工具调用结果: {result}")
            
            if result.content and len(result.content) > 0:
                text_content = result.content[0].text
                logger.info(f"天气信息文本: {text_content}")
                print(f"\n天气信息:\n{text_content}")
            else:
                logger.error("工具调用未返回内容")
                print("\n工具调用未返回内容")
        except Exception as tool_error:
            logger.error(f"调用天气工具时出错: {str(tool_error)}", exc_info=True)
            print(f"\n调用天气工具时出错: {str(tool_error)}")
            
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
        print(f"测试过程中出错: {str(e)}")
    finally:
        await exit_stack.aclose()
        logger.info("测试完成")

async def test_cve_tool():
    """测试CVE漏洞查询工具调用"""
    logger.info("开始测试CVE漏洞查询工具")
    
    exit_stack = AsyncExitStack()
    
    try:
        # 设置环境变量，强制使用 UTF-8 编码
        env = os.environ.copy()
        env["PYTHONIOENCODING"] = "utf-8"
        
        server_params = StdioServerParameters(
            command="python",
            args=["weather_server.py"],
            env=env
        )
        
        # 启动 MCP 服务器并建立通信
        logger.info("启动 MCP 服务器并建立通信")
        stdio_transport = await exit_stack.enter_async_context(stdio_client(server_params))
        stdio, write = stdio_transport
        session = await exit_stack.enter_async_context(ClientSession(stdio, write))
        
        await session.initialize()
        
        # 列出 MCP 服务器上的工具
        response = await session.list_tools()
        tools = response.tools
        tool_names = [tool.name for tool in tools]
        logger.info(f"可用工具: {tool_names}")
        
        # 确认CVE工具可用
        if "query_cve" not in tool_names:
            logger.error("CVE查询工具不可用")
            print("\nCVE查询工具不可用")
            return
        
        # 直接调用CVE查询工具
        logger.info("直接调用CVE查询工具")
        try:
            # 测试一个知名的CVE编号
            cve_id = "CVE-2021-44228"  # Log4Shell 漏洞
            result = await session.call_tool("query_cve", {"cve_id": cve_id})
            
            logger.info(f"工具调用结果类型: {type(result)}")
            logger.info(f"工具调用结果: {result}")
            
            if result.content and len(result.content) > 0:
                text_content = result.content[0].text
                logger.info(f"CVE信息文本: {text_content}")
                print(f"\nCVE信息:\n{text_content}")
            else:
                logger.error("工具调用未返回内容")
                print("\n工具调用未返回内容")
        except Exception as tool_error:
            logger.error(f"调用CVE查询工具时出错: {str(tool_error)}", exc_info=True)
            print(f"\n调用CVE查询工具时出错: {str(tool_error)}")
            
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
        print(f"测试过程中出错: {str(e)}")
    finally:
        await exit_stack.aclose()
        logger.info("测试完成")

async def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有测试")
    
    # 测试天气工具
    await test_weather_tool()
    
    # 测试CVE查询工具
    await test_cve_tool()
    
    logger.info("所有测试完成")

if __name__ == "__main__":
    # 确保控制台使用 UTF-8 编码
    if os.name == 'nt':
        os.system("chcp 65001 > nul")
    
    # 运行单个测试
    # asyncio.run(test_weather_tool())
    asyncio.run(test_cve_tool())
    
    # 运行所有测试
    # asyncio.run(run_all_tests())

