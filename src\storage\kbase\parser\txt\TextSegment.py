import os
import re
import jieba
import hashlib
import copy
from src.config.constant import log
from src.config.memory import stop_txt


class TextSegment:
    """
    chunk_size:块大小
    model:分块模式（token：固定大小）
    """

    def __init__(self, text, chunk_size=50, model="token"):
        self.text = text
        self.chunk_size = chunk_size
        self.model = model
        self.stopwords = stop_txt

    def TextSplitter(self):
        splitter_result = list()
        if self.model == "token":
            splitter_result = self.TokenTextSplitter()
        elif self.model == "char":
            splitter_result = self.CharTextSplitter()
        elif self.model == "semantic":
            splitter_result = self.SemanticTextSplitter()
        else:
            print("不支持当前模式：{}".format(self.model))

        return splitter_result

    # 固定大小块切分
    def TokenTextSplitter(self, content=None):
        if content == None:
            content = self.text
        text_list = []
        text_length = len(content)
        text_size = text_length // self.chunk_size
        for index in range(text_size):
            text_cell = content[index * self.chunk_size: (index + 1) * self.chunk_size]
            text_list.append(text_cell)
        text_list.append(content[text_size * self.chunk_size:])
        return text_list

    # 字符文本切割
    def CharTextSplitter(self, content=None):
        if content == None:
            content = self.text
        text_list = []
        if len(content) < self.chunk_size:
            text_list = [content]
        else:
            text_list_temp = self.CharTextSplitterProcess(content)
            cell = ""
            for i in text_list_temp:
                for ii in i:
                    if len(ii) < self.chunk_size:
                        if len(ii) + len(cell) < self.chunk_size:
                            cell += ii
                        else:
                            text_list.append(cell)
                            cell = ii
                    else:
                        if len(cell) > 0:
                            text_list.append(cell)
                        cell = ""
                        ii_list = self.TokenTextSplitter(ii)
                        text_list.extend(ii_list)
                if len(cell) > 0:
                    text_list.append(cell)
                    cell = ""

        return text_list

        # text_list = self.CharTextSplitterProcess()
        # return text_list

    # 字符文本切割处理流程
    def CharTextSplitterProcess(self, content):
        paragraph = content.split("\n")
        sentence_list = []
        for each_paragraph in paragraph:
            sentence_list.append(self.MyCut(each_paragraph))
        sentence_result = []
        for item in sentence_list:
            list_t = []
            sentence = ""
            flag = True  # 标识
            for cell in item:
                if cell.find('：“') * cell.find('”') >= 0 and flag:
                    list_t.append(cell + sentence)
                else:
                    flag = False
                    sentence = sentence + cell
                    if cell.find('”') > 0:
                        list_t.append(sentence)
                        sentence = ""
                        flag = True
            sentence_result.append(list_t)
        return sentence_result

    # 段落分句
    def MyCut(self, para):
        # 分句规则
        pattern = ['([。！？\?])([^”’])', '(\.{6})([^”’])', '(\…{2})([^”’])', '([。！？\?][”’])([^，。！？\?])']
        for p in pattern:
            para = re.sub(p, r'\1\n\2', para)
        para = para.rstrip()
        return para.split("\n")

    def SemanticTextSplitter(self, content=None):
        if content == None:
            content = self.text
        text_list = []
        paragraphs = content.split("\n")
        flag = len(paragraphs)
        cnt = 1
        while True:
            print("index:", cnt)
            cnt += 1
            print(paragraphs)
            cell = ""
            # for paragraph in paragraphs:
            for i in range(len(paragraphs)):
                paragraph = paragraphs[i]

                if i != len(paragraphs) - 1:
                    paragraph_after = paragraphs[i + 1]
                else:
                    paragraph_after = ""

                if len(cell + paragraph) < self.chunk_size:
                    paragraph_list = jieba.lcut(paragraph)
                    paragraph_list = [i for i in paragraph_list if i not in self.stopwords and len(i.strip()) > 0]
                    paragraph_after_list = jieba.lcut(paragraph_after)
                    paragraph_after_list = [i for i in paragraph_after_list if i not in self.stopwords and len(i.strip()) > 0]
                    paragraph_before_list = jieba.lcut(cell)
                    paragraph_before_list = [i for i in paragraph_before_list if i not in self.stopwords and len(i.strip()) > 0]
                    if len(set(paragraph_list) & set(paragraph_before_list)) > len(set(paragraph_list) & set(paragraph_after_list)):
                        if cell:
                            cell += "\n" + paragraph
                        else:
                            cell = paragraph
                    else:
                        text_list.append(cell)
                        cell = paragraph
                else:
                    text_list.append(cell)
                    cell = paragraph
            if flag == len(text_list):
                break
            else:
                paragraphs = text_list.copy()
                text_list = []
                flag = len(paragraphs)
                cell = ""

        return text_list

def getTextSplite(content):
    '''
        分割字符串
    :param content: 字符串
    :return: 分割后的list
    '''
    textSeg = TextSegment(content, chunk_size=2000)
    return textSeg.TextSplitter()

def getTextDocList(path, name):
    '''
        获取text的分段信息
    :param path: text文件全路径
    :param name: 文件名
    :return: 按照格式的分段信息
    '''
    with open(path, "r", encoding="utf8")as f:
        content = f.read()
    contentList = getTextSplite(content)
    docList = []
    docOneDic = {
        # 文档名
        "book_name": name,
        # 根据文档名生成的ID
        "book_id": hashlib.md5(name.encode("utf8")).hexdigest(),
        "title": name[:-4],
        "catalog": []
    }

    for i in range(len(contentList)):
        currentDocOneDic = copy.deepcopy(docOneDic)
        currentDocOneDic["content"] = contentList[i]
        currentDocOneDic["paragraph_sequence"] = i
        log.info(f"【段落信息】:{currentDocOneDic}")
        docList.append(currentDocOneDic)
    return docList


def textToStr(filePath):
    '''
        将文档解析为纯文本
    :param filePath: 文档全路径
    :return: 文档解析后的全文本
    '''
    with open(filePath, "r", encoding="utf8")as f:
        docStr = f.read()
    return {"status": 200, "message": "ok", "data": os.path.basename(filePath)}, docStr


if __name__ == '__main__':
    text = """宋集薪刚要去抓棋子，齐先生突然说道：“今日你们下一盘座子棋，执白先行。”两个少年一头雾水，皆不知“座子棋”为何物。齐先生语速不急不缓，仔细解释过了规矩后，并不繁琐，只是在四星位分别放下黑白两子。
    宋集薪皱眉思索片刻，很快眼前一亮，眉头舒展道：“是棋盘格局变小了。”然后宋集薪邀功一般，抬头笑问道：“对吧，齐先生？”中年儒士点头道：“确实如此。”
            """
    # words = jieba.lcut(text)
    # print(words)
    # stopwords = open("./data/stopword.txt", "r", encoding="utf8").read().split("\n")
    # stopwords = [i.strip() for i in stopwords]
    # print(stopwords)
    # words = [i for i in words if i not in stopwords and len(i.strip()) > 0]
    # print(words)

    # text = "i love you!" * 100
    textSeg = TextSegment("1234\nqq\n爱上你的" + text, model="semantic")
    print(textSeg.TextSplitter())
