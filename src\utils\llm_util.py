import json
import requests
from src.config.constant import llmUrl
from src.config.constant import llmModel
from src.config.constant import log


def getLlmAnswer(prompt, llm_url=llmUrl, model=llmModel):
    '''
      获取llm的返回结果
    :param prompt: 带有替换模板的提示词，替换格式为{{#参数名#}}
    :param parameters: 替换的值
    :return: llm返回的回复
    '''


    payload = {
        "model": model,
        "prompt": prompt,
        "stream": False
    }
    # 替换参数
    log.info(f"【llm调用问题】: {json.dumps(payload, ensure_ascii=False)}")
    headers = {
        'Content-Type': 'application/json'
    }
    response = requests.request("POST", f"{llm_url}generate", headers=headers, data=json.dumps(payload), verify=False)

    result = json.loads(response.text)

    return result["response"]
