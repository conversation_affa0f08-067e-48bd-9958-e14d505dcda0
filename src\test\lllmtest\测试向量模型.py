import torch
import torch.nn.functional as F

from torch import Tensor
from transformers import AutoTokenizer, AutoModel


def last_token_pool(last_hidden_states: Tensor,
                 attention_mask: Tensor) -> Tensor:
    left_padding = (attention_mask[:, -1].sum() == attention_mask.shape[0])
    if left_padding:
        return last_hidden_states[:, -1]
    else:
        sequence_lengths = attention_mask.sum(dim=1) - 1
        batch_size = last_hidden_states.shape[0]
        return last_hidden_states[torch.arange(batch_size, device=last_hidden_states.device), sequence_lengths]


def get_detailed_instruct(task_description: str, query: str) -> str:
    return f'Instruct: {task_description}\nQuery: {query}'


# Each query must come with a one-sentence instruction that describes the task
task = 'Given a web search query, retrieve relevant passages that answer the query'
queries = [
    get_detailed_instruct(task, 'how much protein should a female eat'),
    get_detailed_instruct(task, 'summit define')
]
# No need to add instruction for retrieval documents
documents = [
    "As a general guideline, the CDC's average requirement of protein for women ages 19 to 70 is 46 grams per day. But, as you can see from this chart, you'll need to increase that if you're expecting or training for a marathon. Check out the chart below to see how much protein you should be eating each day.",
    "Definition of summit for English Language Learners. : 1  the highest point of a mountain : the top of a mountain. : 2  the highest level. : 3  a meeting or series of meetings between the leaders of two or more governments."
]
input_texts = queries + documents

tokenizer = AutoTokenizer.from_pretrained('/app/xtuner-main/gteQwen27b', trust_remote_code=True)
model = AutoModel.from_pretrained('/app/xtuner-main/gteQwen27b', trust_remote_code=True).to("cuda")

max_length = 32000

import os
import time
import traceback
from flask import Flask, request, jsonify, Response
import json
# 加载训练后的模型
app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False
print("模型加载完成")
@app.route("/qte/qwen27b/content", methods=["POST"])
def getChatGLM49b():
    question = ""
    result = ""
    try:
        question = request.get_data(as_text=True)
        print(f"【接收到的问题】:{question}")
        if question is None or question == "":
            result = jsonify({"code": 100, "message": "参数错误"})
        else:
            # Tokenize the input texts
            batch_dict = tokenizer([question], max_length=max_length, padding=True, truncation=True,
                                   return_tensors='pt')
            outputs = model(**batch_dict)
            embeddings = last_token_pool(outputs.last_hidden_state, batch_dict['attention_mask'])

            # normalize embeddings
            embeddings = F.normalize(embeddings, p=2, dim=1)
            result = embeddings.tolist()[0]
    except:
        print(traceback.format_exc())
        result = jsonify({"code": 500, "message": "出现了一个意想不到的错误，请确认参数后重试"})
    return result

@app.route("/qte/qwen27b/question", methods=["POST"])
def getChatGLM49b():
    question = ""
    result = ""
    try:
        question = request.get_data(as_text=True)
        print(f"【接收到的问题】:{question}")
        if question is None or question == "":
            result = jsonify({"code": 100, "message": "参数错误"})
        else:
            # Tokenize the input texts
            batch_dict = tokenizer([get_detailed_instruct(task, question)], max_length=max_length, padding=True, truncation=True,
                                   return_tensors='pt')
            outputs = model(**batch_dict)
            embeddings = last_token_pool(outputs.last_hidden_state, batch_dict['attention_mask'])

            # normalize embeddings
            embeddings = F.normalize(embeddings, p=2, dim=1)
            result = embeddings.tolist()[0]
    except:
        print(traceback.format_exc())
        result = jsonify({"code": 500, "message": "出现了一个意想不到的错误，请确认参数后重试"})
    return result


if __name__ == '__main__':
    app.run('0.0.0.0', 24111)