# 字符判断常用方法

# 判断单字符是否为汉字
def is_chinese(char):
    if len(char) > 1:
        return False
    if u'\u4e00' <= char <= u'\u9fa5':
        return True
    else:
        return False


# 判断单字符是否为数字
def is_number(char):
    if len(char) > 1:
        return False
    if u'\u0030' <= char <= u'\u0039':
        return True
    else:
        return False


# 判断单字符是否为英文字母
def is_english(char):
    if len(char) > 1:
        return False
    if (u'\u0041' <= char <= u'\u005a') or (u'\u0061' <= char <= u'\u007a'):
        return True
    else:
        return False


# 判断是否其他字符（除汉字、数字和英文字符）
def is_other(char):
    if len(char) > 1:
        return False
    if not (is_chinese(char) or is_number(char) or is_english(char)):
        return True
    else:
        return False


# 判断字符串是否全中文
def is_all_chinese(sentence):
    for word in sentence:
        if not ('\u4e00' <= word <= '\u9fa5'):
            return False
    return True


# 用于查找字符或子字符串在字符串中从右至左第一次和第二次出现的索引
def find_first_and_second_index_from_right(s, sub):
    # 初始化索引为-1，表示尚未找到
    first_index = -1
    second_index = -1

    # 获取子字符串的长度
    sub_len = len(sub)

    # 从字符串的右边开始遍历，直到找到两个索引或遍历完整个字符串
    for i in range(len(s) - sub_len + 1, -1, -1):
        # 检查当前位置开始的子字符串是否与要查找的子字符串匹配
        if s[i:i + sub_len] == sub:
            # 如果是第一次找到，则更新first_index
            if first_index == -1:
                first_index = i
            # 如果是第二次找到，则更新second_index并退出循环
            elif second_index == -1:
                second_index = i
                break

    # 返回找到的索引，如果未找到则返回-1, -1
    return first_index, second_index


if __name__ == '__main__':
    print(is_chinese('嘼'))
    print(is_english('y'))

    print(is_number("899"))
    print(is_other("#"))
    print(is_all_chinese("这是测试"))
