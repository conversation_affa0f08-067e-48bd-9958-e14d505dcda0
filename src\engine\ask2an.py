# -*- coding: utf-8 -*-
"""
Create Time: 2024/9/10 14:04
Author: liuyigong
"""

from src.calculate.reranker_server import getRerankerSources, rerank_by_score
from src.calculate.tfidf_ranker import sort_by_query_similarity
from src.service.scene.execute_service import *
from src.service.scene.scene_service import *
from src.service.sensitive.sensitive_service import sensitive_filter
import src.service.firewall.document_service as search_serveice


'''
ask to answer pipeline

'''


def askMe(query, data=dict()):
    #  ---------------------------------------   获取所有参数 -------------------------------------------
    try:
        session_id = data.get('session_id', '')
        kb_list = data.get('kb_list', '')
        kb_list = kb_list.split(",")
        kb_list = [int(kb.strip()) for kb in kb_list if kb.strip() != '']
        doc_list = data.get('doc_list', '')
        session_context = data.get('context', '')
        log.info('\n')
        log.info('\n')
        log.info('\n')
        log.info('-----------------------------------------------------------------------------------')
        log.info('askMe for the sessionId:%s, query:%s, kb_list:%s, time:%s'
                 % (str(session_id), str(query), str(kb_list), str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))))
        log.info('\n')
        log.info((f'获得POST-data: {data}'))
        #  ---------------------------------------   敏感词过滤  -------------------------------------------
        pass
        #  ---------------------------------------   判断意图  -------------------------------------------
        #  --------------问题query向量化------------
        # query_vec = getQueryVec(query)
        # log.info((f'获得query_vec 成功'))
        #  --------------问题拆分------------
        pass

        #  ---------------------------------------   获取外接信息 -------------------------------------------
        #  ----------- es 搜索 -------------------
        es_search_result_list =[]
        es_res = search_serveice.es_search(query=query, index_name=es_doc_index, kbase_ids=kb_list)
        if es_res:
            es_search_result_list = es_res
        log.info(f"es 召回相关信息共{len(es_search_result_list)}条")
        log.info(f"es 召回相关content_id为{[i['content_id'] for i in es_search_result_list]}")
        log.info(f'es-top3 的标题分别为：{[i["title"] for i in es_search_result_list[0:min(3, len(es_search_result_list))]]}')
        #  ----------- Milvus 搜索 -------------------
        milivs_search_result_list =[]
        milvus_res = search_serveice.milvus_search(query=query, index_name=es_doc_index, collection_name=milvus_index, kbase_ids=kb_list)
        if milvus_res:
            milivs_search_result_list = milvus_res
        log.info(f"milvus 召回相关信息共{len(es_search_result_list)}条")
        log.info(f"milvus 召回相关content_id为{[i['content_id'] for i in milivs_search_result_list]}")
        log.info(f'milvus-top3 的标题分别为：{[i["title"] for i in milivs_search_result_list[0:min(3, len(milivs_search_result_list))]]}')
        #  ----------- 两个搜索结果合并和粗排 -------------------
        res_list = sort_by_query_similarity(es_search_result_list, milivs_search_result_list, query)

        log.info(f'粗排-top3 的标题分别为：{[i["title"] for i in res_list[0:min(3, len(res_list))]]}')
        #  ----------- 粗排列表进入bce-rerank模型重排 -------------------
        log.info(f'粗排后的数据取中值以上部分')
        res_list = [i for i in res_list[:len(res_list) // 2]]
        content_list = [i["content"] for i in res_list]
        score_list = getRerankerSources(query=query, contentList=content_list)
        sorted_res_list = rerank_by_score(res_list, score_list)
        #  ----------- 重排结果最终处理 -------------------
        n = 3
        top_n = sorted_res_list[0:min(n, len(sorted_res_list))]
        log.info(f"重排序后相关content_id为{[i['content_id'] for i in sorted_res_list]}")
        log.info(f'top3 的标题分别为：{[i["title"] for i in top_n]}')

        #
        #  ---------------------------------------   富化 -------------------------------------------
        # log.info(execute_result)
        # enrichment_ans="跳过模型富化"
        # TODO 敏感词过滤太慢了，暂时取消
        # last_res_list = [sensitive_filter(i["content"]) for i in top_n]
        last_res_list = [i["content"] for i in top_n]
        last_res = "|".join(last_res_list)
        last_res = last_res[:min(3000, len(last_res))]
        enrichment_ans = answer_enrichment(query=query, execute_result=last_res, scene_intention="根据给出的已知信息回答问题")
        enrichment_ans.replace("Thought: Do I need to use a tool? No", "")
        enrichment_ans.replace("Thought: Do I need to use a tool? No\nAI:", "")
        return {'code': 200, "msg": "success", "data": {"llm_ans": enrichment_ans, "doc_source": top_n}}
    except Exception as e:
        log.error(Log().trace_back_info())
        return {'code': 202, "msg": "wrong", "data": {}}
    #

def askMeNoLLM(query, data=dict()):
    #  ---------------------------------------   获取所有参数 -------------------------------------------
    try:
        session_id = data.get('session_id', '')
        kb_list = data.get('kb_list', '')
        kb_list = kb_list.split(",")
        kb_list = [int(kb.strip()) for kb in kb_list if kb.strip() != '']
        doc_list = data.get('doc_list', '')
        h5flag = data.get('h5flag', 0)
        session_context = data.get('context', '')
        log.info('\n')
        log.info('\n')
        log.info('\n')
        log.info('-----------------------------------------------------------------------------------')
        log.info('askMe for the sessionId:%s, query:%s, kb_list:%s, time:%s'
                 % (str(session_id), str(query), str(kb_list), str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))))
        log.info('\n')
        log.info((f'获得POST-data: {data}'))
        #  ---------------------------------------   敏感词过滤  -------------------------------------------
        pass
        #  ---------------------------------------   判断意图  -------------------------------------------
        #  --------------问题query向量化------------
        # query_vec = getQueryVec(query)
        # log.info((f'获得query_vec 成功'))
        #  --------------问题拆分------------
        pass

        #  ---------------------------------------   获取外接信息 -------------------------------------------
        #  ----------- es 搜索 -------------------
        es_search_result_list =[]
        es_res = search_serveice.es_search(query=query, index_name=es_doc_index, kbase_ids=kb_list)
        if es_res:
            es_search_result_list = es_res
        log.info(f"es 召回相关信息共{len(es_search_result_list)}条")
        log.info(f"es 召回相关content_id为{[i['content_id'] for i in es_search_result_list]}")
        log.info(f'es-top3 的标题分别为：{[i["title"] for i in es_search_result_list[0:min(3, len(es_search_result_list))]]}')
        #  ----------- Milvus 搜索 -------------------
        milivs_search_result_list =[]
        milvus_res = search_serveice.milvus_search(query=query, index_name=es_doc_index, collection_name=milvus_index, kbase_ids=kb_list)
        if milvus_res:
            milivs_search_result_list = milvus_res
        log.info(f"milvus 召回相关信息共{len(es_search_result_list)}条")
        log.info(f"milvus 召回相关content_id为{[i['content_id'] for i in milivs_search_result_list]}")
        log.info(f'milvus-top3 的标题分别为：{[i["title"] for i in milivs_search_result_list[0:min(3, len(milivs_search_result_list))]]}')
        #  ----------- 两个搜索结果合并和粗排 -------------------
        res_list = sort_by_query_similarity(es_search_result_list, milivs_search_result_list, query)

        log.info(f'粗排-top3 的标题分别为：{[i["title"] for i in res_list[0:min(3, len(res_list))]]}')
        #  ----------- 粗排列表进入bce-rerank模型重排 -------------------
        log.info(f'粗排后的数据取中值以上部分')
        if len(res_list) > 6:
            res_list = [i for i in res_list[:len(res_list) // 2]]
        content_list = [i["content"] for i in res_list]
        score_list = getRerankerSources(query=query, contentList=content_list)
        sorted_res_list = rerank_by_score(res_list, score_list)
        #  ----------- 重排结果最终处理 -------------------
        n = 3
        top_n = sorted_res_list[0:min(n, len(sorted_res_list))]
        top_n = [
            {**item, 'content': sensitive_filter(item['content'])}
            for item in top_n
        ]  ## 解包并每个元素中的content字段用sensitive_filter()方法过滤一遍



        log.info(f"重排序后相关content_id为{[i['content_id'] for i in sorted_res_list]}")
        log.info(f'top3 的标题分别为：{[i["title"] for i in top_n]}')

        if h5flag:
            n = 10
            top_n = sorted_res_list[0:min(n, len(sorted_res_list))]
            top_n = [
                {**item,
                 'url': f'{es_ip}:{9253}/getContent?content_id={item["content_id"]}',
                 'score': 1
                 }
                for item in top_n
            ]
            return {'number_of_results': len(top_n), "query": query, "results": top_n}

        #
        #  ---------------------------------------   富化 -------------------------------------------
        # log.info(execute_result)
        enrichment_ans="跳过模型富化"
        # last_res_list = [i["content"] for i in top_n]
        # last_res = "|".join(last_res_list)
        # last_res = last_res[:min(3000, len(last_res))]
        # enrichment_ans = answer_enrichment(query=query, execute_result=last_res, scene_intention="根据给出的已知信息回答问题")
        return {'code': 200, "msg": "success", "data": {"llm_ans": enrichment_ans, "doc_source": top_n}}
    except Exception as e:
        log.error(Log().trace_back_info())
        return {'code': 202, "msg": "wrong", "data": {}}
    #




def get_content_by_id(content_id):
    #  ---------------------------------------   获取所有参数 -------------------------------------------
    try:
        log.info('\n')
        log.info('\n')
        log.info(f'查询content_id 为{content_id}')
        log.info('-----------------------------------------------------------------------------------')
        #  ----------- es 搜索 -------------------
        es_res = search_serveice.es_search(query="", index_name=es_doc_index, content_ids=content_id)
        log.info(f"es 召回相关信息共{len(es_res)}条")
        log.info(f"es 召回相关content_id的title为{[i['title'] for i in es_res]}")
        h5_str = f'''
            <html><title></title><body></body></html>
            '''
        if es_res:
            h5_str = f'''
            <html><title>{es_res[0].get('title')}</title><body>{es_res[0].get('content')}</body></html>
            '''

        return h5_str


    except Exception as e:
        log.error(Log().trace_back_info())
        return {'code': 202, "msg": "wrong", "data": {}}