from elasticsearch import Elasticsearch
from elasticsearch.exceptions import ConnectionError, AuthenticationException
import urllib3
import warnings
# 禁用所有警告
urllib3.disable_warnings()
# 禁用所有警告
warnings.filterwarnings("ignore")

'''
本地环境（版本高了不行）
elasticsearch==7.13.1
numpy==1.26.4

pwsh中执行：

PS E:\program\python\MCP\mcp-client> Test-NetConnection ********** -Port 9222

ComputerName     : **********
RemoteAddress    : **********
RemotePort       : 9222
InterfaceAlias   : 以太网 3
SourceAddress    : ************
TcpTestSucceeded : True

PS E:\program\python\MCP\mcp-client> curl -u admin:18902a0b4adff375f73d --insecure https://**********:9222
{
  "name" : "node-1",
  "cluster_name" : "infinilabs",
  "cluster_uuid" : "ZJq3z3RmR9eAfHyR7RdPgA",
  "version" : {
    "distribution" : "easysearch",
    "number" : "1.12.1",
    "distributor" : "INFINI Labs",
    "build_hash" : "0203b705a709ed8de4ce95543a9897748088ca5c",
    "build_date" : "2025-04-28T14:14:41.375184Z",
    "build_snapshot" : false,
    "lucene_version" : "8.11.4",
    "minimum_wire_lucene_version" : "7.7.0",
    "minimum_lucene_index_compatibility_version" : "7.7.0"
  },
  "tagline" : "You Know, For Easy Search!"
}
'''


try:
    es = Elasticsearch(
        hosts=["https://**********:9222"],
        http_auth=('admin', '18902a0b4adff375f73d'),
        verify_certs=False,
        request_timeout=30  # 增加超时时间
    )
    
    # 获取完整集群信息
    print("连接成功！集群信息：")
    print(es.info())
    
    # 测试索引操作
    if not es.indices.exists(index="test_ping"):
        es.indices.create(index="test_ping")
        print("测试索引创建成功")
    
except AuthenticationException as e:
    print(f"认证失败: {str(e)}")
except ConnectionError as e:
    print(f"连接错误: {str(e)}")
    print("可能原因：")
    print("1. Easysearch 服务未启动")
    print("2. 端口映射错误")
    print("3. 防火墙阻止了连接")
except Exception as e:
    print(f"未知错误: {str(e)}")