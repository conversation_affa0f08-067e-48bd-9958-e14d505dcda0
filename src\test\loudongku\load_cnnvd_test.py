

import os
import json


'''
读取指定目录下的cnnvd的json格式数据，并录入到知识库中
'''
#
#
# raw_json = json.load(open(r'E:\program\python\rag_dev\data\save\files\3253883558\format.1742887602707.json', "r", encoding="utf8"))
#
# print(raw_json)


import os
import requests
from tqdm import tqdm

def upload_json_files(base_path, url, kbase_id, model):
    """
    上传指定路径下的所有 JSON 文件到指定接口，支持断点续传和显示进度
    :param base_path: JSON 文件所在的基础路径
    :param url: 上传接口的 URL
    :param kbase_id: 知识库 ID
    :param model: 分段形式（chapter/length）
    :return: 无
    """
    # 获取所有 JSON 文件的路径列表
    file_paths = []
    for root, dirs, files in os.walk(base_path):
        for file in files:
            if file.endswith(".json"):
                file_paths.append(os.path.join(root, file))

    # 检查已上传的文件，避免重复上传
    uploaded_files = set()
    if os.path.exists("uploaded_files.txt"):
        with open("uploaded_files.txt", "r") as f:
            uploaded_files = set(f.read().splitlines())

    # 使用 tqdm 显示进度条
    with tqdm(total=len(file_paths), desc="上传进度") as pbar:
        for file_path in file_paths:
            file_name = os.path.basename(file_path)
            # 如果文件已经上传过，则跳过
            if file_name in uploaded_files:
                pbar.update(1)
                continue

            try:
                # 准备文件上传
                files = [
                    ('file', (file_name, open(file_path, 'rb'), 'application/json'))
                ]
                headers = {
                    'Accept': '*/*',
                    'Connection': 'keep-alive',
                }
                mapping = '''
                {"title": {"join": ["vulName","cveCode","cnnvdCode"]},"book_name": {"source": "filename"},"summary": {"join": ["cveCode","cnnvdCode"]},"content": {"source": "rawjson"}}
                '''

                # 发送 POST 请求
                response = requests.post(url, headers=headers, files=files, params={"kbase_id": kbase_id, "model": model, "mapping": mapping})

                # 检查响应状态
                if response.status_code == 200:
                    # 如果上传成功，记录文件名
                    with open("uploaded_files.txt", "a") as f:
                        f.write(file_name + "\n")
                    pbar.update(1)
                else:
                    print(f"文件 {file_name} 上传失败，状态码：{response.status_code}")
                    print(response.text)

            except Exception as e:
                print(f"文件 {file_name} 上传出错：{e}")

    print("所有文件上传完成")

# 示例用法
if __name__ == "__main__":
    base_path = "/home/<USER>/apps/nlp/loophole/cnnvd/form"
    url = "http://10.91.4.40:9252/file/upload"
    kbase_id = "2205124352"
    model = "auto"

    upload_json_files(base_path, url, kbase_id, model)