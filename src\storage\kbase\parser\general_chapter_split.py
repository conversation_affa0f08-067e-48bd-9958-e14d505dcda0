import asyncio
import json
from src.config.constant import *
from src.config.constant import chunks_max_tokens, chunks_overlap
from src.utils.llm_util import getLlmAnswer
from src.utils.prompt_util import *
from src.utils.file_util import calculateMd5File
import hashlib
import uuid
from tqdm import tqdm
from datetime import datetime
from src.utils.id_util import genNoRepeatNum10Id
from src.calculate.embedding_server import getContentVecAsync
from src.utils.elasticsearch_async_util import asyncElasticSearchEngine
from src.config.constant import es_doc_index
import src.storage.kbase.parser.general_length_split as generalLengthSplit

def generate_combined_id(book_title):
    md5 = hashlib.md5()
    md5.update(book_title.encode('utf-8'))
    base_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, md5.hexdigest())
    return str(base_uuid)

def get_paragraph_summary(paragraph):
    prompt_id = "summary_extractor"
    placeholders = {"paragraph": paragraph}
    prompt_content = filled_prompts_placeholder(prompt_id, placeholders)
    summary = getLlmAnswer(prompt_content, llm_url=llmModel)
    return summary

async def generateInsertDicAsync(kbaseId, fileName, filePath, docDicList, parameters):
    '''
    异步将按照章节切分的文档，进行字段扩充
    :param kbaseId: 知识库id
    :param fileName: 文件名
    :param filePath: 文件路径
    :param docDicList: 章节解析后结果，[ { "title": "xxxx||xxxx||xxx", "content": "xxxxxxxx" } ]
    :param parameters: 参数字典
    :return: ES插入列表和Milvus插入列表
    '''
    # 获取参数
    maxTokens = parameters.get("maxTokens", chunks_max_tokens)
    chunkOverlap = parameters.get("chunkOverlap", chunks_overlap)
    model = parameters.get("model", "auto")
    if not maxTokens: maxTokens = chunks_max_tokens
    if not chunkOverlap: chunkOverlap = chunks_overlap

    # 初始化列表
    esInsertList = []
    milvusInsertList = []
    contentList = []
    
    # 生成不与库中id重复的新id
    docId = 0
    for genNum in range(10):
        docId = genNoRepeatNum10Id()  # 简单的ID生成
        # 数据库查询操作，使用异步
        if not await asyncElasticSearchEngine.find_field(field="doc_id", field_value=docId, index_name=es_doc_index):
            break
        else:
            docDicList = []
    
    # 计算文件MD5
    docMd5 = await asyncio.to_thread(calculateMd5File, filePath)
    
    # 处理文档列表
    if docDicList and len(docDicList) > 0:
        index = 0
        for doc_chunk in docDicList:
            do_split_content = doc_chunk["content"]
            if model == "json" and isinstance(do_split_content, dict):
                done_split = [json.dumps(doc_chunk["content"], ensure_ascii=False)]
            elif model == "json" and isinstance(do_split_content, str):
                done_split = [do_split_content]
            else:
                # 文本分割
                done_split = generalLengthSplit.doSplit(do_split_content, maxTokens, chunkOverlap)
            
            for docOne in done_split:
                if index % 10 == 0:
                    log.info(f"【{fileName}数据生成进度】:{index}/{len(docDicList)}")
                
                # 构建ES文档
                esInsert = {
                    "doc_name": fileName,
                    "doc_name_keyword": fileName,
                    "doc_md5": docMd5,
                    "doc_id": docId,
                    "content_id": docId * 1000000 + index,
                    "title": doc_chunk['title'],
                    "content": docOne,
                    "summary": doc_chunk.get('summary', ''),
                    "model": parameters.get('model', "auto"),
                    "kbase_id": kbaseId,
                    "create_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                }
                
                # 构建Milvus文档
                milvusInsert = {
                    "content_id": esInsert["content_id"],
                    "doc_id": esInsert["doc_id"],
                    "kbase_id": esInsert["kbase_id"],
                }
                
                contentList.append(docOne)
                esInsertList.append(esInsert)
                milvusInsertList.append(milvusInsert)
                index += 1
    else:
        log.info("将按照章节切分的章节列表为空，无法形成入库数据。")

    log.info("【批量向量化开始】")
    # 批量向量化
    needVecList = []
    contentVecList = []
    batchSize = 5
    
    for i in range(len(contentList)):
        if i % 10 == 0:
            log.info(f"【{fileName}批量向量化进度】:{i}/{len(contentList)}")
        
        needVecList.append(contentList[i])
        if len(needVecList) == batchSize:
            # 向量化是计算密集型操作，使用异步
            batch_vectors = await getContentVecAsync(needVecList)
            contentVecList.extend(batch_vectors)
            needVecList = []
        elif len(contentList) - 1 == i:
            # 处理最后一批，使用异步
            batch_vectors = await getContentVecAsync(needVecList)
            contentVecList.extend(batch_vectors)
            needVecList = []
    
    log.info("【批量向量化完成】")
    
    # 填充向量到Milvus数据 - 简单的内存操作，保持同步
    for i in range(len(contentVecList)):
        milvusInsertList[i]["embedding"] = contentVecList[i]

    return esInsertList, milvusInsertList
