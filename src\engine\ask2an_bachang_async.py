# -*- coding: utf-8 -*-
"""
Create Time: 2025/5/26 14:04
Author: liuyigong
"""
import asyncio
import time
from typing import Dict
import requests
import re
import json
from src.config.constant import *
import src.service.firewall.document_service_async as search_serveice
from src.service.firewall.document_service_async import search_async
from src.utils.llm_async_util import getLlmAnswerAsync
from src.calculate.tfidf_ranker import sort_by_query_similarity
'''
ask to answer pipeline
'''

from src.service.kbase.kbase_service_async import listKbase

async def intention(query: str) -> str:
    """
    生成意图识别提示
    
    参数:
        query: 用户查询
        
    返回:
        格式化的提示文本
    """
    
    prompt_intention = """
    你是一个智能助手，需要帮助用户理解查询并确定最佳的知识库来回答问题。
    请执行以下两个任务后返回JSON：

    【任务一】根据问题内容选择需要检索的知识库ID（kbase_id）
    【任务二】为每个选中的知识库优化query以提升检索效果

    可用知识库：
    {{#kbase_desc#}}

    判断规则：
    1. 当问题涉及基础理论概念时选择: {KBASE_001}
    2. 当需要操作流程指导时选择: {KBASE_002}
    3. 当涉及具体漏洞详情时选择: {KBASE_003}
    4. 当需要企业级解决方案时选择: {KBASE_004}

    优化建议：
    1. 若涉及漏洞情报，调整补充CVE编号格式（不要捏造）
    2. 若涉及企业方案，添加「企业级」「甲方」等限定词
    3. 技术类问题使用标准术语（如用「SSL/TLS」替代「安全加密」）

    示例：
    问题：服务器被入侵该怎么处理？
    回答：
    ```json
    {
      "kbase_ids": ["{{#KBASE_002#}}", "{{#KBASE_004#}}"],
      "queries": {
        "{{#KBASE_002#}}": "网络安全应急响应流程：服务器入侵处置步骤",
        "{{#KBASE_004#}}": "服务器被入侵的企业级处置方案" 
      }
    }
    ```

    问题：AES和RSA加密的区别是什么？
    回答：
    ```json
    {
    	"kbase_ids": ["{{#KBASE_001#}}"],
    	"queries": {
        "{{#KBASE_001#}}": "AES的加密方式是什么？",
        "{{#KBASE_001#}}": "RSA的加密方式是什么？" 
      }
    }
    ```
    当前问题：{{#question#}}
    请严格按上述JSON格式回答：
    """
    
    
    try:
        # 异步获取知识库列表
        kbase_list_response = await listKbase()
        kbase_list_data = json.loads(kbase_list_response)
        
        if kbase_list_data.get("status") == 200:
            kbases = kbase_list_data.get("data", [])
            kbase_desc = "\n".join([
                f"ID: {kb.get('kbaseId')} - 名称: {kb.get('kbaseName')} - 描述: {kb.get('desc')}"
                for kb in kbases
            ])
            
            KBASE_001 = ""
            KBASE_002 = ""
            KBASE_003 = ""
            KBASE_004 = ""
            for i in kbases:
                if "basic" in i['kbaseName']:
                    KBASE_001 = i['kbaseId']
                if "policy" in i['kbaseName']:
                    KBASE_002 = i['kbaseId']
                if "loophole" in i['kbaseName']:
                    KBASE_003 = i['kbaseId']
                if "solutions" in i['kbaseName']:
                    KBASE_004 = i['kbaseId']
            
        else:
            return "无可用知识库"

        placeholder_dict = {
            "question": query,
            "kbase_desc": kbase_desc,
            "KBASE_001": KBASE_001,
            "KBASE_002": KBASE_002,
            "KBASE_003": KBASE_003,
            "KBASE_004": KBASE_004
        }
        log.info(f"placeholder_dict:{placeholder_dict}")
        # 格式化提示模板
        for i, (k, v) in enumerate(placeholder_dict.items()):
            prompt_intention = prompt_intention.replace('{{#' + str(k) + '#}}',
                                                        '"' + str(v) + '"' if isinstance(v, str)
                                                        else json.dumps(v, ensure_ascii=False))
        
        log.info("完整的意图识别prompt为：")
        log.info(prompt_intention)
        
        return prompt_intention
    except Exception as e:
        log.error(f"生成意图识别提示时出错: {str(e)}")
        log.error(traceback.format_exc())
        return "无可用知识库"


async def generate_text_async(model: str = "deepseek-r1:32b", prompt: str = "", stream: bool = False):
    """
    异步调用生成文本的API

    参数:
        model (str): 使用的模型名称
        prompt (str): 输入的提示文本
        stream (bool): 是否使用流式输出，默认为False

    返回:
        API的响应结果
    """
    if not prompt:
        log.info("prompt 为空")
        return

    return await getLlmAnswerAsync(prompt, llm_url="http://kubemlsvc.qianxin-inc.cn:80/svc-d7vd819rjkxh/api/", model=model)


def generate_text(model: str = "deepseek-r1:32b", prompt: str = "", stream: bool = False):
    """
    调用生成文本的API

    参数:
        model (str): 使用的模型名称
        prompt (str): 输入的提示文本
        stream (bool): 是否使用流式输出，默认为False

    返回:
        API的响应结果
    """
    if not prompt:
        log.info("prompt 为空")
        return

    url = "http://kubemlsvc.qianxin-inc.cn:80/svc-d7vd819rjkxh/api/generate"

    payload = json.dumps({
        "model": model,
        "prompt": prompt,
        "stream": stream
    })

    headers = {
        'Content-Type': 'application/json',
        'Connection': 'keep-alive'
    }

    try:
        response = requests.post(url, headers=headers, data=payload)
        response.raise_for_status()  # 检查请求是否成功
        # print(response)
        response_text = response.json().get("response", "")

        # 使用正则表达式移除<think>标签及其内容（包括换行符）
        cleaned_response = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL)

        return cleaned_response.strip().replace("```json", "").replace("```", "")
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return {"error": str(e)}
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return {"error": "Invalid JSON response"}


async def search_docs_async(query: str, data: Dict) -> Dict:
    """
    异步搜索文档
    
    参数:
        query: 搜索查询
        data: 包含kb_list等参数的字典
        
    返回:
        搜索结果
    """
    try:
        kb_list = data.get('kb_list', '')
        if not kb_list:
            return {'code': 202, "msg": "kb_list is empty", "data": {}}
            
        # 调用异步搜索服务
        search_result = await search_async(query, kb_list)
        return search_result
    except Exception as e:
        log.error(f"搜索文档时出错: {str(e)}")
        return {'code': 202, "msg": f"搜索失败: {str(e)}", "data": {}}


async def search_docs(query, data=dict()):
    #  ---------------------------------------   获取所有参数 -------------------------------------------
    try:
        session_id = data.get('session_id', '')
        kb_list = data.get('kb_list', '')
        kb_list = kb_list.split(",")
        kb_list = [int(kb.strip()) for kb in kb_list if kb.strip() != '']
        #  ---------------------------------------   敏感词过滤  -------------------------------------------
        pass

        #  ---------------------------------------   获取外接信息 -------------------------------------------
        #  ----------- es 搜索 -------------------
        es_search_result_list = []
        es_res = await search_serveice.es_search_async(query=query, index_name=es_doc_index, kbase_ids=kb_list)
        if es_res:
            es_search_result_list = es_res
        log.info(f"es 召回相关信息共{len(es_search_result_list)}条")
        log.info(f"es 召回相关content_id为{[i['content_id'] for i in es_search_result_list]}")
        log.info(
            f'es-top3 的标题分别为：{[i["title"] for i in es_search_result_list[0:min(3, len(es_search_result_list))]]}')
        #  ----------- Milvus 搜索 -------------------
        milivs_search_result_list = []
        milvus_res = await search_serveice.milvus_search_async(query=query, index_name=es_doc_index, collection_name=milvus_index,
                                                   kbase_ids=kb_list)
        if milvus_res:
            milivs_search_result_list = milvus_res
        log.info(f"milvus 召回相关信息共{len(es_search_result_list)}条")
        log.info(f"milvus 召回相关content_id为{[i['content_id'] for i in milivs_search_result_list]}")
        log.info(
            f'milvus-top3 的标题分别为：{[i["title"] for i in milivs_search_result_list[0:min(3, len(milivs_search_result_list))]]}')
        #  ----------- 两个搜索结果合并和粗排 -------------------
        res_list = sort_by_query_similarity(es_search_result_list, milivs_search_result_list, query)

        log.info(f'粗排-top3 的标题分别为：{[i["title"] for i in res_list[0:min(3, len(res_list))]]}')
        #  ----------- 粗排列表进入bce-rerank模型重排 -------------------
        log.info(f'跳过重排序')
        sorted_res_list = res_list

        # log.info(f'粗排后的数据取中值以上部分')
        # if len(res_list) > 6:
        #     res_list = [i for i in res_list[:len(res_list) // 2]]
        # content_list = [i["content"] for i in res_list]
        # score_list = getRerankerSources(query=query, contentList=content_list)
        # sorted_res_list = rerank_by_score(res_list, score_list)
        #  ----------- 重排结果最终处理 -------------------
        n = 3
        top_n = sorted_res_list[0:min(n, len(sorted_res_list))]
        # top_n = [
        #     {**item, 'content': sensitive_filter(item['content'])}
        #     for item in top_n
        # ]  ## 解包并每个元素中的content字段用sensitive_filter()方法过滤一遍
        #
        # log.info(f"重排序后相关content_id为{[i['content_id'] for i in sorted_res_list]}")
        log.info(f'top{n} 的标题分别为：{[i["title"] for i in top_n]}')
        #
        #  ---------------------------------------   富化 -------------------------------------------
        enrichment_ans = "跳过模型富化"
        last_res_list = [{i["title"]: i["content"]} for i in top_n]
        # last_res = "|".join(last_res_list)
        # last_res = last_res[:min(3000, len(last_res))]
        return {"data": last_res_list}
    except Exception as e:
        log.error(Log().trace_back_info())
        return {"data": {}}
    #

async def process_intention_result(llm_result: Dict, original_query: str) -> Dict:
    """
    异步处理意图识别结果，并行调用askMeNoLLM并汇总结果

    参数:
        llm_result: 意图识别结果，包含kbase_ids和queries
        original_query: 用户的原始查询

    返回:
        包含所有检索结果和原始查询的字典
    """
    
    # 解析LLM结果
    try:
        if isinstance(llm_result, str):
            llm_result = json.loads(llm_result)
        
        queries = llm_result.get("queries", {})
        if not queries:
            log.warning("意图识别结果中没有查询")
            return {"original_query": original_query, "results": []}
        
        # 创建异步任务列表
        tasks = []
        for kb_id, query in queries.items():
            # 为每个知识库创建一个搜索任务
            task = search_docs(query, {"kb_list": kb_id, "session_id": ''})
            tasks.append(task)
        
        # 并行执行所有搜索任务
        search_results = await asyncio.gather(*tasks)
        
        # 处理搜索结果
        unique_results = []
        for kb_id, result in zip(queries.keys(), search_results):
            if result:
                # 提取搜索结果中的文档
                docs = result.get("data", [])
                if docs:
                    for doc in docs:
                        new_var = {
                            "kb_id": kb_id,
                            "doc": doc
                        }
                        if new_var not in unique_results:
                            unique_results.append(new_var)
    
            
        return {"original_query": original_query, "results": unique_results}
    except Exception as e:
        log.error(f"异步搜索时出错: {str(e)}")
        log.error(traceback.format_exc())
        return {"original_query": original_query, "results": []}


async def generate_final_answer(context: Dict) -> tuple:
    """
    异步生成最终答案
    
    参数:
        context: 包含原始查询和检索结果的字典
        
    返回:
        生成的最终答案和格式化的检索信息
    """
    try:
        original_query = context.get("original_query", "")
        results = context.get("results", [])
        
        if not results:
            retrieved_info = "当前没有搜索到相关的知识，请你直接回答用户问题。"
        else:
            # 格式化检索到的信息
            # 整理检索到的信息
            retrieved_info = ""
            for i, result in enumerate(results, 1):
                retrieved_info += f"\n【来源{i}】\n"
                retrieved_info += f"文档内容: {result.get('doc', '无内容')}\n"

        
        # 构建提示模板
        prompt_template = """
        你是一个专业的问答助手，负责根据提供的知识信息回答用户的问题。
        角色：你是个聪明的助手。你的名字是Q-RAG。
        任务：从知识库中总结信息，回答用户的问题。
        要求和限制：
        -尽可能用知识库中的与问题相关的信息和原文回答。
        -不要编造，尤其是数字。
        -回答markdown格式的文本。
        -回答用户的问题的语言。
        -不要编造，尤其是数字。

        用户问题: 
        ```question
        {original_query}
        ```

        以下是从知识库中检索到的相关信息:
        ```kbase
        {retrieved_info}
        ```
        
        请根据上述信息回答用户的问题。如果提供的信息不足以回答问题，请坦诚告知。
        回答应该简洁明了，直接针对用户的问题。不要在回答中提及你是基于检索信息回答的。
        """
        
        prompt = prompt_template.format(
            original_query=original_query,
            retrieved_info=retrieved_info
        )
        
        log.info(f"调用大模型生成最终答案")
        # 异步调用大模型生成最终答案
        final_answer = generate_text(prompt=prompt)
        return final_answer, retrieved_info
    except Exception as e:
        log.error(f"生成最终答案时出错: {str(e)}")
        log.error(traceback.format_exc())
        return "抱歉，生成答案时出现错误。", None


async def askMe(query: str, data: Dict = None) -> Dict:
    """
    异步处理用户查询并生成回答
    
    参数:
        query: 用户查询
        data: 包含会话ID等信息的字典
        
    返回:
        包含生成答案的字典
    """
    if data is None:
        data = {}
        
    try:
        session_id = data.get('session_id', '')
        log.info('\n')
        log.info('\n')
        log.info('\n')
        log.info('-----------------------------------------------------------------------------------')
 
        log.info(f'askMe for the sessionId:{session_id}, query:{query}, time:{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))}')
        log.info(f'获得POST-data: {data}')
        
        # 生成意图识别提示
        prompt_total = await intention(query=query)
        
        # 调用LLM进行意图识别
        response = generate_text(prompt=prompt_total)
        
        try:
            llm_result = json.loads(response)
            log.info(f"大模型返回: {llm_result}")
        except json.JSONDecodeError:
            log.error(f"无法解析大模型返回的JSON: {response}")
            llm_result = {"queries": {}}

        # 异步处理意图识别结果
        context = await process_intention_result(llm_result, query)
        
        # 异步生成最终答案
        final_answer, retrieved_info = await generate_final_answer(context)
        
        # 添加分隔符和检索信息
        final_answer += "\n =============================== \n"
        final_answer += retrieved_info
        
        return {'code': 200, "msg": "success", "data": {"llm_ans": final_answer, "doc_source": []}}
    except Exception as e:
        log.error(f"处理查询时出错: {str(e)}")
        log.error(traceback.format_exc())
        return {'code': 202, "msg": "生成答案时出现错误。", "data": {}}


async def askMeNoLLM(query: str, data: Dict = None) -> Dict:
    """
    异步处理用户查询，只返回检索结果，不调用LLM生成答案
    
    参数:
        query: 用户查询
        data: 包含kb_list等参数的字典
        
    返回:
        包含检索结果的字典
    """
    if data is None:
        data = {}
        
    try:
        kb_list = data.get('kb_list', '')
        if not kb_list:
            return {'code': 202, "msg": "kb_list is empty", "data": {}}
        
        # 异步搜索文档
        search_result = await search_docs(query, data)
        
        if search_result.get("code") == 200:
            return search_result
        else:
            return {'code': 202, "msg": "搜索失败", "data": {}}
    except Exception as e:
        log.error(f"处理查询时出错: {str(e)}")
        return {'code': 202, "msg": f"处理查询时出错: {str(e)}", "data": {}}




