import os
import re
import copy
import hashlib
import pdfplumber
from pypdf import PdfReader
from src.config.constant import log





def getOutlineInfo(path, lastLevel):
    '''
    获取pdf的目录信息
    :param path: pdf文件全路径
    :param lastLevel: 文档目录保留最低等级
    :return: [{"title": 目录标题,
                "pageNum": 目录页码,
                "subtree": [子目录信息]},]的list
    '''
    # 目录信息
    outlineInfoList = []
    # 读取pdf
    reader = PdfReader(path)
    # 获取目录标签
    outline = reader.outline

    def getTitleAndPageNum(outlineInfoList, currentOutline, outlineTree=[]):
        # 遍历目录标签
        for one in currentOutline:
            if isinstance(one, list):
                if len(outlineTree) >= lastLevel - 1:
                    continue
                # 获取目录树结构
                currentTree = []
                # 获取上级目录树结构
                if len(outlineTree) > 0:
                    for outlineOne in outlineTree:
                        currentTree.append(outlineOne)
                currentTree.append(outlineInfoList[-1]["title"])
                # 获取子目录
                getTitleAndPageNum(outlineInfoList[-1]["subtree"], one, currentTree)
            else:
                # 构建{"title": 目录标题,
                #  "pageNum": 目录页码,
                #  "subtree": [子目录信息]}的list

                outlineInfoList.append({
                    "title": one.title.strip(),
                    "pageNum": int(reader.get_page_number(one.page)),
                    "subtree": [],
                    "tree": outlineTree
                })

    # 获取目录信息
    getTitleAndPageNum(outlineInfoList, outline)

    return outlineInfoList

def replaceTableToMarkdown(currentTextList, tablesTextList):
    '''
        替换表格为markdown格式
    :param currentTextList: 原始文本list
    :param tablesTextList: 表格信息list
    :return:
    '''
    # 遍历所有表格
    for oneTable in tablesTextList:
        # 获取表格头字符串形式，以定为替换的位置
        tableStart = ""
        for oneAtt in oneTable[0]:
            tableStart += oneAtt.split("\n")[0] + " "
        tableStart = tableStart.strip()
        # 制作表格的markdown格式
        tableMarkDownList = []
        for oneLine in oneTable:
            if oneLine.count(None) > 0:
                for elementIndex in range(len(oneLine)):
                    if oneLine[elementIndex] is None:
                        oneLine[elementIndex] = ""
            currentRaw = "|" + "|".join(oneLine).strip() + "|"
            currentRaw = currentRaw.replace("\n", "")
            tableMarkDownList.append(currentRaw)
            # 首行后需要拼接特定的符号
            if len(tableMarkDownList) == 1:
                tableMarkDownList.append("|" + "|".join(["---" for _ in range(len(oneLine))]) + "|")
        # 存在表格则替换对应的内容
        if currentTextList.count(tableStart) > 0:
            index = currentTextList.index(tableStart)
            currentTextList = currentTextList[:index] + tableMarkDownList + currentTextList[(1 + index + len(oneTable) + str(oneTable).count("\\n")):]
    return currentTextList

def getPdfDocList(path, name, lastLevel):
    '''
        获取pdf的分段信息
    :param path: pdf文件全路径
    :param name: 文件名
    :param lastLevel: 文档目录保留最低等级
    :return: 按照格式的分段信息
        {
        

        }
    '''
    outlineInfoList = getOutlineInfo(path, lastLevel)
    with pdfplumber.open(path) as currentPdf:
        docList = []
        docOneDic = {
            # 文档名
            "book_name": name,
            # 根据文档名生成的ID
            "book_id": hashlib.md5(name.encode("utf8")).hexdigest()
        }
        def getCurrentChapterContent(pdfTextDicList, chapterList, lastChapterPageInfo):
            currentChapter = chapterList[-2]
            nextChapter = chapterList[-1]
            # 获取当前章节的首页页码
            firstPageNum = currentChapter["pageNum"] if currentChapter else 0
            # 获取该章节的最后页码
            lastPageNum = nextChapter["pageNum"] if nextChapter else (len(currentPdf.pages) - 1)
            # 遍历章节所在的页码
            textList = []
            for pageNum in range(firstPageNum, lastPageNum + 1):
                # 获取该页的内容
                currentText = currentPdf.pages[pageNum].extract_text().strip()
                # 替换重复过多的无意义字符
                currentText = re.sub(r"\.{6,}", "......", currentText)
                # 以回车分割为list
                currentTextList = currentText.split("\n")
                # 如果是该章节的首页标题为最后一行，直接跳过
                if pageNum == lastChapterPageInfo[-1]["page"] and lastChapterPageInfo[-1]["line"] >= len(
                    currentTextList):
                    continue
                # 获取当页总文字行数
                thisPageLine = -1
                if nextChapter and pageNum == lastPageNum:
                    currentTextTempStr = ""
                    for pageLine in range(lastChapterPageInfo[-1]["line"] if pageNum == firstPageNum else 0,
                                          len(currentTextList)):
                        currentTextTempStr += currentTextList[pageLine].strip().replace(" ","")
                        if nextChapter["title"].replace(" ","") in currentTextTempStr:
                            # 处理章节标题不在同一行的情况
                            if pageLine != 0 and currentTextList[pageLine].strip().replace(" ","") != nextChapter["title"].replace(" ",""):
                                thisPageLine = pageLine - 1
                                break
                            else:
                                thisPageLine = pageLine
                                break
                # 如果是该章节的第一页，截取上次的剩余部分，否则从头截取
                currentTextStartLine = lastChapterPageInfo[-1]["line"] + 1 if pageNum == firstPageNum else 0
                # 如果是该章节的最后一页，截取到下一章节的标题前
                currentTextEndLine = thisPageLine if thisPageLine != -1 else len(currentTextList)
                currentTextList = currentTextList[currentTextStartLine:currentTextEndLine]
                # 替换其中的表格为markdown
                try:
                    currentTextList = replaceTableToMarkdown(currentTextList, currentPdf.pages[pageNum].extract_tables())
                except:
                    log.info("【表格替换失败】")
                textList += currentTextList

                if pageNum == lastPageNum:
                    lastChapterPageInfo.append({
                        "page": pageNum,
                        "line": thisPageLine
                    })
            pdfTextDicList.append(textList)
            # 章节结构
            docOneDic["catalog"] = currentChapter["tree"] if currentChapter else []
            log.info(currentChapter["tree"] if currentChapter else [])
            # 文章内容
            docOneDic["content"] = "\n".join(textList).strip()
            log.info(f"文章内容切分为{len(textList)}段落")
            # log.info("\n".join(textList).strip())
            # 文章属于分段后的第几段
            docOneDic["paragraph_sequence"] = len(docList)
            if docOneDic["content"] != "":
                docOneDeepCopy = copy.deepcopy(docOneDic)
                docList.append(docOneDeepCopy)

        # 上一次提取到达的页数和行数
        lastChapterPageInfo = [{
            "page": 0,
            "line": 0
        }]
        chapterList = [None]
        # pdf数据
        pdfTextDicList = []
        outlineInfoList.append(None)

        def getChapterInfo(outlineInfoList):
            for currentChapter in outlineInfoList:
                chapterList.append(currentChapter)
                log.info("-------------------------------")
                # 段落所处的标题
                docOneDic["title"] = chapterList[-2]["title"] if chapterList[-2] else ""
                log.info(chapterList[-2]["title"] if chapterList[-2] else [])
                log.info("-------------------------------")
                # log.info(chapterList[-1]["title"])
                getCurrentChapterContent(pdfTextDicList, chapterList, lastChapterPageInfo)
                if currentChapter and len(currentChapter["subtree"]) > 0:
                    getChapterInfo(currentChapter["subtree"])

        getChapterInfo(outlineInfoList)
        # log.info(pdfTextDicList)
        # 新版本的解析后结果，title(全章节信息路径) 和 content(该章节内容)
        # [ { "title": "xxxx||xxxx||xxx", "content": "xxxxxxxx" } ]
        clearDocList = []
        for part in docList:
            fullCatalog = part["catalog"]
            fullCatalog.append(part["title"])
            clearDocList.append(
                {
                    "title": "||".join(fullCatalog),
                    "content": part["content"]
                }
            )
        uploadInfo = {"status": 200, "message": "解析成功", "data": name}
        return uploadInfo, clearDocList


def pdfQualityJudgment(docList):
    '''
        判断pdf解析质量
    :param docList: 解析后的数据
    :return:
    '''

    allContent = ""
    for oneDoc in docList:
        allContent += oneDoc["content"] + "\n"
    if allContent.count("cid") > (len(allContent) * 0.05):
        return {"status": 201, "message": "pdf中包含无法识别的字体，解析失败", "data": docList[0]["book_name"]}, docList
    return {"status": 200, "message": "ok", "data": docList[0]["book_name"]}, docList

def pdfToStr(filePath):
    '''
        将文档解析为纯文本
    :param filePath: 文档全路径
    :return: 文档解析后的全文本
    '''
    docStr = ""
    # 读取pdf
    with pdfplumber.open(filePath) as currentPdf:
        # 按页获取pdf内容
        for onePage in currentPdf.pages:
            docStr += onePage.extract_text().strip() + "\n"
    docStr = docStr.strip()
    # 判断字体转换是否出现问题
    if docStr.count("cid") > (len(docStr) * 0.05):
        return {"status": 201, "message": "pdf中包含无法识别的字体，解析失败", "data": os.path.basename(filePath)}, docStr
    # 替换重复过多的无意义字符
    docStr = re.sub(r"\.{6,}", "......", docStr)
    return {"status": 200, "message": "ok", "data": os.path.basename(filePath)}, docStr
