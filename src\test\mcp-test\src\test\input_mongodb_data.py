import pandas as pd
from pymongo import MongoClient
import urllib.parse
# data = {
#     "name": ["马军军", "李杰", "黄娜", "马伟", "李强", "吴娜娜", "胡军军", "徐浩", "罗兰", "何强", "杨强强", "李涵涵", "赵阳", "徐阳", "徐芳", "唐宇", "王宇宇", "赵静静", "何伟", "吴浩", "黄涵涵", "刘强", "周伟", "孙娜娜", "郑杰杰", "章浩", "刘静", "胡阳阳", "朱超", "罗娜娜", "张伟"],
#     "gender": ["男", "男", "女", "男", "男", "女", "男", "男", "女", "男", "男", "女", "女", "男", "女", "女", "男", "女", "女", "男", "男", "男", "男", "女", "男", "女", "男", "女", "男", "女", "男", "女", "男"],
#     "birthDate": ["2005-01-15T00:00:00.000Z", "2005-02-15T00:00:00.000Z", "2005-03-15T00:00:00.000Z", "2005-04-15T00:00:00.000Z", "2005-05-15T00:00:00.000Z", "2005-06-15T00:00:00.000Z", "2005-07-15T00:00:00.000Z", "2005-08-15T00:00:00.000Z", "2005-09-15T00:00:00.000Z", "2005-10-15T00:00:00.000Z", "2005-11-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z", "2005-12-15T00:00:00.000Z"],
#     "enrollDate": ["2023-08-31T16:00:00.000Z"] * 30,
#     "contact": {
#         "phone": ["125mmffy9f", "1bhew9qtr", "1fqns3tmm", "124h9ld6tj", "170hj3npsv", "1qzqxpmex", "13pml86sjd", "1fqns3tmm", "110uahbdf", "1fqy3osif79", "12sh9ke7q", "12h35aofr", "1637q4ekx", "1fpkmne5f", "15vp8b2ek", "12se8ubk5", "18exq35br", "14xem2ud8", "13l55df6f", "179de29e", "179qja79g", "6l9pblphj", "18vdl38w9c", "11efzfduqe", "11jhz2n1f9", "12pia3qzs", "168l64kg2", "16reh8w0", "11mf05uach"],
#         "email": ["202301_1", "202301_2", "202301_3", "202301_4", "202301_5", "202301_6", "202301_7", "202301_8", "202301_9", "202301_10", "202301_11", "202301_12", "202301_13", "202301_14", "202301_15", "202301_16", "202301_17", "202301_18", "202301_19", "202301_20", "202301_21", "202301_22", "202301_23", "202301_24", "202301_25", "202301_26", "202301_27", "202301_28", "202301_29", "202301_30", "202301_31"],
#         "address": ["13k62c7a FellfortHyatt Mountains 2", "136qzvoua East VillaLavonne Burg 3", "19xvwijc Lake ValentineAlbin Mar", "131egtr04k WaukeshaDickinson Villag", "136j9r2jzn HarrisburgMorar Plaza 1#", "13alzj9l1 Costa MesaAlexandre Ave", "1394vwz0c KeelingboroughStamm Roa", "13yjhmu9j EvelinebergSchovler Villa", "13l6hf1t0 North ElouiseRae Staven", "132lm3p3k MissoulaHarran Bypass 1#", "13r3brzgkg KenoshaRansom Street 2#", "13y4wqhlh Longmontzell Camp 3#", "19q3x4y4s Lake HelagburyStromon S", "13rbwm0h2 West LehamhounHousin", "15c13osx191 North Khalton Langos", "13je8c5ek Port BerycesterFrida Pla", "13exq35br ErzatzdorfLorian Lights 3#", "13xzllynw New AlejandroburyEdward", "13quq79lb OptoportMatteo View 5#k", "13v8qj3er Jurupa ValleyKendrick T", "13w8bqoat West RusoffCasper Cent", "1329npzcp PerriHarris Thoroughway 2", "131i42ejw North CarlingbergHedge", "13zlb0ud4 ConnlanDunfeather Avenue 5#", "13pyqulck Carson CityKovacek Ville", "13clxelwv RyarfortAdell Park 4#2", "13nv78t58 West MariahQuigley Ville", "13nyr52b6 ReddingPrecious Canyon 4", "13qy5o58b WinifredFort Godfrey Pines", "13k6rh8wox Albamarle Of Fjor...1oad", "13qr2aqjda KelystarRydn Causeway"]
#     },
#     "profile": {
#         "h": [166, 167, 157, 169, 170, 171, 172, 173, 164, 175, 176, 176, 167, 178, 179, 170, 181, 182, 173, 184, 185, 176, 187, 188, 179, 190, 191, 182, 193, 194, 185, 196],
#         "profile": ["95.2529", "56.7749", "59.9101", "64.3701", "53.1729", "56.6181", "64.6873", "62.6618", "53.6390", "51.6493", "53.9557", "69.4874", "57.9277", "51.9840", "54.5908", "57.5840", "52.0602", "62.6103", "50.7067", "56.6362", "59.9393", "61.4673", "61.6279", "58.7189", "61.6075", "52.1766", "58.0157", "8.3424", "65.3795", "57.5253", "59.0629"],
#         "health": ["合格", "合格", "良好", "良好", "良好", "优秀", "合格", "优秀", "优秀", "优秀", "合格", "良好", "良好", "优秀", "合格", "良好", "优秀", "良好", "合格", "合格", "合格", "合格", "优秀", "优秀", "合格", "优秀", "优秀", "优秀", "合格", "优秀", "优秀", "优秀", "优秀"]
#     }
# }

data= [
    {
      "name": "马军军",
      "gender": "男",
      "birthDate": "2005-01-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "125mmffy9f",
        "email": "202301_1",
        "address": "13k62c7a FellfortHyatt Mountains 2"
      },
      "profile": {
        "h": 166,
        "profile": "95.2529",
        "health": "合格"
      }
    },
    {
      "name": "李杰",
      "gender": "男",
      "birthDate": "2005-02-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "1bhew9qtr",
        "email": "202301_2",
        "address": "136qzvoua East VillaLavonne Burg 3"
      },
      "profile": {
        "h": 167,
        "profile": "56.7749",
        "health": "合格"
      }
    },
    {
      "name": "黄娜",
      "gender": "女",
      "birthDate": "2005-03-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "1fqns3tmm",
        "email": "202301_3",
        "address": "19xvwijc Lake ValentineAlbin Mar"
      },
      "profile": {
        "h": 157,
        "profile": "59.9101",
        "health": "良好"
      }
    },
    {
      "name": "马伟",
      "gender": "男",
      "birthDate": "2005-04-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "124h9ld6tj",
        "email": "202301_4",
        "address": "131egtr04k WaukeshaDickinson Villag"
      },
      "profile": {
        "h": 169,
        "profile": "64.3701",
        "health": "良好"
      }
    },
    {
      "name": "李强",
      "gender": "男",
      "birthDate": "2005-05-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "170hj3npsv",
        "email": "202301_5",
        "address": "136j9r2jzn HarrisburgMorar Plaza 1#"
      },
      "profile": {
        "h": 170,
        "profile": "53.1729",
        "health": "良好"
      }
    },
    {
      "name": "吴娜娜",
      "gender": "女",
      "birthDate": "2005-06-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "1qzqxpmex",
        "email": "202301_6",
        "address": "13alzj9l1 Costa MesaAlexandre Ave"
      },
      "profile": {
        "h": 171,
        "profile": "56.6181",
        "health": "优秀"
      }
    },
    {
      "name": "胡军军",
      "gender": "男",
      "birthDate": "2005-07-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "13pml86sjd",
        "email": "202301_7",
        "address": "1394vwz0c KeelingboroughStamm Roa"
      },
      "profile": {
        "h": 172,
        "profile": "64.6873",
        "health": "合格"
      }
    },
    {
      "name": "徐浩",
      "gender": "男",
      "birthDate": "2005-08-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "1fqns3tmm",
        "email": "202301_8",
        "address": "13yjhmu9j EvelinebergSchovler Villa"
      },
      "profile": {
        "h": 173,
        "profile": "62.6618",
        "health": "优秀"
      }
    },
    {
      "name": "罗兰",
      "gender": "女",
      "birthDate": "2005-09-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "110uahbdf",
        "email": "202301_9",
        "address": "13l6hf1t0 North ElouiseRae Staven"
      },
      "profile": {
        "h": 164,
        "profile": "53.6390",
        "health": "优秀"
      }
    },
    {
      "name": "何强",
      "gender": "男",
      "birthDate": "2005-10-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "1fqy3osif79",
        "email": "202301_10",
        "address": "132lm3p3k MissoulaHarran Bypass 1#"
      },
      "profile": {
        "h": 175,
        "profile": "51.6493",
        "health": "优秀"
      }
    },
    {
      "name": "杨强强",
      "gender": "男",
      "birthDate": "2005-11-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "12sh9ke7q",
        "email": "202301_11",
        "address": "13r3brzgkg KenoshaRansom Street 2#"
      },
      "profile": {
        "h": 176,
        "profile": "53.9557",
        "health": "合格"
      }
    },
    {
      "name": "李涵涵",
      "gender": "女",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "12h35aofr",
        "email": "202301_12",
        "address": "13y4wqhlh Longmontzell Camp 3#"
      },
      "profile": {
        "h": 176,
        "profile": "69.4874",
        "health": "良好"
      }
    },
    {
      "name": "赵阳",
      "gender": "女",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "1637q4ekx",
        "email": "202301_13",
        "address": "19q3x4y4s Lake HelagburyStromon S"
      },
      "profile": {
        "h": 167,
        "profile": "57.9277",
        "health": "优秀"
      }
    },
    {
      "name": "徐阳",
      "gender": "男",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "1fpkmne5f",
        "email": "202301_14",
        "address": "13rbwm0h2 West LehamhounHousin"
      },
      "profile": {
        "h": 178,
        "profile": "51.9840",
        "health": "良好"
      }
    },
    {
      "name": "徐芳",
      "gender": "女",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "15vp8b2ek",
        "email": "202301_15",
        "address": "15c13osx191 North Khalton Langos"
      },
      "profile": {
        "h": 179,
        "profile": "54.5908",
        "health": "良好"
      }
    },
    {
      "name": "唐宇",
      "gender": "男",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "12se8ubk5",
        "email": "202301_16",
        "address": "13je8c5ek Port BerycesterFrida Pla"
      },
      "profile": {
        "h": 170,
        "profile": "57.5840",
        "health": "优秀"
      }
    },
    {
      "name": "王宇宇",
      "gender": "女",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "18exq35br",
        "email": "202301_17",
        "address": "13exq35br ErzatzdorfLorian Lights 3#"
      },
      "profile": {
        "h": 181,
        "profile": "52.0602",
        "health": "优秀"
      }
    },
    {
      "name": "赵静静",
      "gender": "女",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "14xem2ud8",
        "email": "202301_18",
        "address": "13xzllynw New AlejandroburyEdward"
      },
      "profile": {
        "h": 182,
        "profile": "62.6103",
        "health": "良好"
      }
    },
    {
      "name": "何伟",
      "gender": "男",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "13l55df6f",
        "email": "202301_19",
        "address": "13quq79lb OptoportMatteo View 5#k"
      },
      "profile": {
        "h": 173,
        "profile": "50.7067",
        "health": "合格"
      }
    },
    {
      "name": "吴浩",
      "gender": "男",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "179de29e",
        "email": "202301_20",
        "address": "13v8qj3er Jurupa ValleyKendrick T"
      },
      "profile": {
        "h": 184,
        "profile": "56.6362",
        "health": "合格"
      }
    },
    {
      "name": "黄涵涵",
      "gender": "男",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "179qja79g",
        "email": "202301_21",
        "address": "13w8bqoat West RusoffCasper Cent"
      },
      "profile": {
        "h": 185,
        "profile": "59.9393",
        "health": "合格"
      }
    },
    {
      "name": "刘强",
      "gender": "男",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "6l9pblphj",
        "email": "202301_22",
        "address": "1329npzcp PerriHarris Thoroughway 2"
      },
      "profile": {
        "h": 176,
        "profile": "61.4673",
        "health": "优秀"
      }
    },
    {
      "name": "周伟",
      "gender": "女",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "18vdl38w9c",
        "email": "202301_23",
        "address": "131i42ejw North CarlingbergHedge"
      },
      "profile": {
        "h": 187,
        "profile": "61.6279",
        "health": "优秀"
      }
    },
    {
      "name": "孙娜娜",
      "gender": "女",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "11efzfduqe",
        "email": "202301_24",
        "address": "13zlb0ud4 ConnlanDunfeather Avenue 5#"
      },
      "profile": {
        "h": 188,
        "profile": "58.7189",
        "health": "良好"
      }
    },
    {
      "name": "郑杰杰",
      "gender": "男",
      "birthDate": "2005-12-15T00:00:00.000Z",
      "enrollDate": "2023-08-31T16:00:00.000Z",
      "contact": {
        "phone": "11jhz2n1f9",
        "email": "202301_25",
        "address": "13pyqulck Carson CityKovacek Ville"
      },
      "profile": {
        "h": 179,
        "profile": "61.6075",
        "health": "合格"
      }
    }   
]


# 将数据转换为 DataFrame
df = pd.DataFrame(data)

# 连接到 MongoDB
# 对用户名和密码进行转义
username = urllib.parse.quote_plus('bachang')
password = urllib.parse.quote_plus('bachang@123')

# 连接到 MongoDB
client = MongoClient(f'mongodb://{username}:{password}@**********:27017/', serverSelectionTimeoutMS=5000)
db = client.studentManagement  # 数据库名称
collection = db.students  # 集合名称

# 创建数据库和集合（如果不存在）
if not db.list_collection_names():
    db.create_collection(collection.name)

# 将数据导入 MongoDB
data = df.to_dict(orient='records')  # 将 DataFrame 转换为字典列表
collection.insert_many(data)

print("数据导入完成！")