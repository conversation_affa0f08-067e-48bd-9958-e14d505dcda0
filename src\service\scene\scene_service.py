# -*- coding: utf-8 -*-
"""
Create Time: 2024/9/10 11:17
Author: liuyigong
"""

from src.config.constant import *
from src.utils.prompt_util import *
from src.utils.llm_util import *

'''
分类
提参
富化
'''


def scene_classifier(query, scene_list, scene_intention):
    log.info("Start predicting scenarios...")

    prompt_id = "scene_classifier"
    placeholders = {"user_query": query,
                    "scene_list": scene_list,
                    "scene_intention": scene_intention
                    }

    return llm_answer(prompt_id, placeholders)


def parameter_extractor(query, parameters, session_context, scene_intention):
    log.info("Start extracting parameters...")

    prompt_id = "parameter_extractor"
    placeholders = {
        "parameters": parameters,
        "scene_intention": scene_intention,
        "session_context": session_context,
        "user_query": query
    }
    return llm_answer(prompt_id, placeholders)


def answer_enrichment(session_context="", query="", execute_result="", scene_intention="", session_prompt=None):
    log.info("Start answer enrichment...")

    # prompt_id = "answer_enrichment"
    prompt_id = "answer_enrichment_zh"
    if session_prompt:
        return llm_answer("0", {}, session_prompt)

    placeholders = {
        "session_context": "",
        "scene_intention": scene_intention,
        "execute_result": execute_result,
        "user_query": query
    }
    return llm_answer(prompt_id, placeholders)


def llm_answer(prompt_id, placeholders, session_prompt=None):
    if not session_prompt:
        prompt_content = filled_prompts_placeholder(prompt_id, placeholders)
    else:
        prompt_content = session_prompt

    llm_answer = getLlmAnswer(prompt_content)

    log.info(f"LLM answer :{llm_answer}")
    return llm_answer

# scene_classifier("外面下雨了，坐那趟车去海洋馆", [
#     {"scene_id": "1", "scene_intention": "天气的状况"},
#     {"scene_id": "2", "scene_intention": "公交车的路线"},
#     {"scene_id": "3", "scene_intention": "其他"}], "出行线路规划")
