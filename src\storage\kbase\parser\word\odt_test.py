from odf.opendocument import load
from odf.text import P, H
# 读取 .odt 文件
doc = load("ODT.odt")
# Create a list to store the paragraphs and headings
content_list = []

# Iterate through text elements and store them with their index
for idx, element in enumerate(doc.getElementsByType(P) + doc.getElementsByType(H)):
    content_list.append((idx, element))

# Sort the list based on the original index
content_list.sort(key=lambda x: x[0])

# # Output the sorted paragraphs and headings
for _, element in content_list:
    if isinstance(element, P):
        print("正文:", element.text)
    elif isinstance(element, H):
        print("标题:", element.text)
