# -*- coding: utf-8 -*-
"""
Create Time: 2024/9/10 14:04
Author: liuyigong
"""

from src.config.constant import *
from src.service.scene.scene_service import *
from src.service.scene.config_service import *
from src.service.scene.execute_service import *
import time
import copy

'''
ask to answer pipeline

'''


def askMe(query, data=dict()):
    #  ---------------------------------------   获取所有参数 -------------------------------------------

    scene_id = data.get('scene_id', '')
    scene_intention = data.get('scene_intention', '')
    session_id = data.get('session_id', '')
    session_context = data.get('session_context', '')
    session_prompt = data.get('session_prompt', '')
    log.info('\n')
    log.info('\n')
    log.info('\n')
    log.info('-----------------------------------------------------------------------------------')
    log.info('askMe for the sessionId:%s, query:%s, scene_intention:%s, time:%s'
             % (str(session_id), str(query), str(scene_intention),
                str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))))
    log.info('\n')
    log.info((f'获得POST-data: {data}'))
    #  ---------------------------------------   判断意图 -------------------------------------------
    scene_all = sceneAll()
    scene_list = scene_all.get('data', {}).get('list', [])
    if not scene_id:
        # if not scene_intention:
        #     return {'code': 202, "msg": "scene_intention empty", "data": {}}

        scene_list_placeholder = [{k: v for k, v in d.items() if k != 'name'} for d in scene_list]
        scene_info = scene_classifier(query, scene_list_placeholder, scene_intention)
        try:
            predict = json.loads(scene_info)
            scene_id = predict.get("scene_id", '')
            scene_intention = predict.get("scene_intention", '')
        except Exception as e:
            log.error(traceback.format_exc())
            return {'code': 202, "msg": "scene classifier wrong", "data": {}}
#  ---------------------------------------   提取参数 -------------------------------------------
    scene_data = getScene(scene_id)
    config_params = scene_data.get('parameter_extractor', [])
    llm_predict_params_str = parameter_extractor(query, config_params, session_context, scene_intention)
    try:
        llm_predict_params = json.loads(llm_predict_params_str)
        # 处理需要查询多个数据，但是api只支持单个查询的情况
        ListStringParamsDic = {}
        for paramConfig in config_params:
            if paramConfig["name"] not in llm_predict_params.keys():
                llm_predict_params[paramConfig["name"]] = ""
            else:
                # 当参数类别为list[string]时，添加到多次调用参数字典中
                if paramConfig["type"] == "list[string]":
                    valueList = []
                    if isinstance(llm_predict_params[paramConfig["name"]], list):
                        if len(llm_predict_params[paramConfig["name"]]) == 1:
                            llm_predict_params[paramConfig["name"]] = llm_predict_params[paramConfig["name"]][0]
                            continue
                        else:
                            valueList = llm_predict_params[paramConfig["name"]]
                    if "," in llm_predict_params[paramConfig["name"]]:
                        valueList = llm_predict_params[paramConfig["name"]].split(",")
                    clearValueList = []
                    for value in valueList:
                        if value.strip() == "":
                            continue
                        else:
                            clearValueList.append(value)
                    if len(clearValueList) > 1:
                        ListStringParamsDic[paramConfig["name"]] = clearValueList

        # 需要调用的参数列表
        llmPredictParamsList = []
        llmPredictParamsList.append(llm_predict_params)

        # 遍历多次调用的变量生成调用参数
        for paramName in ListStringParamsDic.keys():
            tempLlmPredictParamsList = copy.deepcopy(llmPredictParamsList)
            for currentParams in tempLlmPredictParamsList:
                for paramValue in ListStringParamsDic[paramName]:
                    currentPredictParams = copy.deepcopy(currentParams)
                    currentPredictParams[paramName] = paramValue
                    llmPredictParamsList.append(currentPredictParams)
                llmPredictParamsList.remove(currentParams)

    except Exception as e:
        log.error(traceback.format_exc())
        return {'code': 202, "msg": "parameter extractor wrong", "data": {}}

    # scene_data = next((item for item in scene_list if item['scene_id'] == 2), None)

    #  ---------------------------------------   获取外接信息 -------------------------------------------
    try:
        execute_result = []
        config_scene_process = scene_data.get('scene_process', [])
        for line in config_scene_process:
            for llmPredictParamsOne in llmPredictParamsList:
                do_execute = doExecute(line, llmPredictParamsOne)
                execute_result.append(do_execute)

    except Exception as e:
        log.error(traceback.format_exc())
        return {'code': 202, "msg": "do execute wrong", "data": {}}

    #  ---------------------------------------   富化 -------------------------------------------
    # log.info(execute_result)
    try:
        enrichment_ans = answer_enrichment(session_context, query, execute_result, scene_intention, session_prompt=session_prompt)
        return {'code': 200, "msg": "success", "data": enrichment_ans}
    except Exception as e:
        log.error(traceback.format_exc())
        return {'code': 202, "msg": "do execute wrong", "data": {}}
    pass
    #
