import base64

from src.config.constant import *
from docx import Document
from docx.text.paragraph import Paragraph
from docx.image.image import Image
from docx.parts.image import ImagePart
from docx.oxml.shape import CT_Picture
from PIL import Image
from src.storage.kbase.parser.word.concate_data import *
import datetime

def is_possible_heading(paragraph, min_font_size=12, bold=True):
    if not paragraph.runs:
        return False

    run = paragraph.runs[0]
    font_size = run.font.size.pt if run.font.size is not None else None
    is_bold = run.bold

    if font_size is not None and font_size >= min_font_size and is_bold == bold:
        return True
    return False


def get_hyperlink(p: Paragraph):
    # 获取超链接文本
    # 将文档处理为xml，并转为字符串
    xml = p.paragraph_format.element.xml
    xml_str = str(xml).replace('\n', '')
    # 获取文本中由<w:hyperlink>标签包起来的部分
    hl_list = re.findall('<w:hyperlink[\S\s]*?</w:hyperlink>|<w:r[\S\s]*?</w:r>', xml_str)
    text = []
    for hyperlink in hl_list:
        # 获取文本中由<w:t>标签包起来的部分
        wt_list = re.findall('<w:t[\S\s]*?</w:t>', hyperlink)
        temp = u''
        for wt in wt_list:
            # 去掉<w:t>标签
            wt_content = re.sub('<[\S\s]*?>', u'', wt)
            temp += wt_content
        text.append(temp)
    return text


def get_pictures(document: Document, paragraph: Paragraph):
    """
    document 为文档对象
    paragraph 为内嵌图片的某一个段落对象，比如第1段内
    """
    result_list = []
    img_list = paragraph._element.xpath('.//pic:pic')
    if len(img_list) == 0 or not img_list:
        return
    for i in range(len(img_list)):
        img: CT_Picture = img_list[i]
        embed = img.xpath('.//a:blip/@r:embed')[0]
        related_part: ImagePart = document.part.related_parts[embed]
        image: Image = related_part.image
        result_list.append(image)
    return result_list


def get_tables_from_paragraph(paragraph):
    """
    从 Word 文档段落中提取表格数据
    :param paragraph: lxml.etree._Element, Word 文档中的段落元素
    :return: list, 包含表格内容的列表，每个元素为一个二维列表，表示一个表格
    """
    tables = []
    # 判断当前段落是否包含表格
    if paragraph._element.xpath('.//w:tbl'):
        table = []
        # 遍历段落中的所有元素
        for element in paragraph._element.iter():
            # 如果遍历到了表格元素，则将表格添加到表格列表中，并重置表格变量
            if element.tag.endswith('tbl'):
                table.append(get_table_content(element))
                tables.append(table)
                table = []
            # 如果遍历到了行元素，则将行添加到表格变量中
            elif element.tag.endswith('tr'):
                row = []
                for cell in element.findall('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tc'):
                    row.append(cell.text)
                table.append(row)
    return tables


def get_table_content(paragraph):
    table_element = None
    ele = paragraph._p.getnext()
    for element in paragraph._element:
        if element.tag == '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tbl':
            table_element = element
            break
    if table_element is None:
        return None

    rows = []
    for tr in table_element.findall('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tr'):
        cells = []
        for tc in tr.findall('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tc'):
            cell_content = []
            for element in tc.iter():
                if element.tag == '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}t':
                    cell_content.append(element.text)
                elif element.tag == '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}p':
                    cell_content.append(get_paragraph_content(element))
                elif element.tag == '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}r':
                    cell_content.append(get_run_content(element))
                elif element.tag == '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}drawing':
                    cell_content.append(get_drawing_content(element))
                elif element.tag == '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}pict':
                    cell_content.append(get_picture_content(element))
            cells.append(cell_content)
        rows.append(cells)
    return rows


def get_paragraph_content(element):
    return element.text


# 获取指定文本元素中的文本内容
def get_run_content(element):
    return element.text


# 获取指定绘图元素中的图片内容
def get_drawing_content(element):
    pic = element.find(
        './/{http://schemas.openxmlformats.org/drawingml/2006/picture}blipFill/{http://schemas.openxmlformats.org/drawingml/2006/picture}blip')
    if pic is not None:
        rId = pic.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
        image_part = element.part.related_parts[rId]
        return base64.b64encode(image_part.blob).decode('utf-8')
    return None


# 获取指定图片元素中的图片内容
def get_picture_content(element):
    pic = element.find(
        './/{http://schemas.openxmlformats.org/drawingml/2006/picture}blipFill/{http://schemas.openxmlformats.org/drawingml/2006/picture}blip')
    if pic is not None:
        rId = pic.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}link')
        image_part = element.part.related_parts[rId]
        return base64.b64encode(image_part.blob).decode('utf-8')
    return None


from docx.document import Document as _Document
from docx.oxml.table import CT_Tbl
from docx.table import _Cell, Table, _Row
import re
from docx.oxml.text.paragraph import CT_P


def iter_block_items(parent):
    if isinstance(parent, _Document):
        parent_elm = parent.element.body
    elif isinstance(parent, _Cell):
        parent_elm = parent._tc
    elif isinstance(parent, _Row):
        parent_elm = parent._tr
    else:
        raise ValueError("something'book_name not right")
    for child in parent_elm.iterchildren():
        if isinstance(child, CT_P):
            yield Paragraph(child, parent)
        if isinstance(child, CT_Tbl):
            yield Table(child, parent)


def extract_docx_content(docx_file):
    document = Document(docx_file)
    for section in document.sections:
        section.different_first_page_header_footer = False
        section.header.is_linked_to_previous = True

    structured_data = {"document": []}
    current_section = None
    last_title_level = None

    for block in iter_block_items(document):
        if isinstance(block, Paragraph) and 'Table' not in block.style.name:
            paragraph = block
            paragraph_text = paragraph.text
            if block.text.strip() == '' or block.text.strip().isdigit():
                continue

            if "目 录" in str(paragraph.text).strip():
                log.info("目 录:")
                log.info(paragraph.text)
            # print(paragraph.text)
            # 提取段落中的图片
            pictures = []
            try:
                pictures = get_pictures(document, paragraph)
            except Exception as e:
                print(e)
                log.error(Log().trace_back_info())
            # tables = get_tables_from_paragraph(paragraph)
            if pictures and pictures != []:
                for pic in pictures:
                    img_base64 = base64.b64encode(pic.blob).decode('utf-8')
                    bs64 = "data:image/jpeg;base64," + img_base64
                    if current_section is None:
                        current_section = {"type": "section", "title": "image_sec", "content": []}
                    current_section["content"].append({"type": "image", "value": bs64[:100]})
                    # current_section["content"].append({"type": "image", "value": pic})
            # elif paragraph.style.name.startswith('Heading') or is_possible_heading(paragraph):
            theme_title_level = None
            if paragraph.style.name.startswith('Block'):
                # print(paragraph.style.name)
                # print(last_title_level)
                theme_title_level = int(last_title_level) + 1
            if (paragraph.style.name.startswith('Heading')
                or "标题" in paragraph.style.name) \
                    or theme_title_level is not None:
                if paragraph.text == "": continue
                heading_level = paragraph.style.name
                if "标题" in paragraph.style.name:
                    try:
                        heading_level = 'Heading ' + str(int(paragraph.style.name.split('标题')[-1]))
                    except:
                        # heading_level = 'Heading ' + str(int(re.findall(r'\d+', paragraph.style.name)[0]) - 1)  # 天擎的一级标题需得级号-1
                        findall = re.findall(r'\d+', paragraph.style.name)
                        if findall:
                            heading_level = 'Heading ' + str(int(findall[0]))
                elif theme_title_level:
                    heading_level = 'Heading ' + str(theme_title_level)

                current_heading_level = heading_level.split(' ')[-1]
                if (last_title_level is None or current_heading_level > last_title_level) \
                        and not theme_title_level:
                    last_title_level = current_heading_level
                current_section = {"type": "section", "title": heading_level + ' ' + paragraph.text, "content": []}
                structured_data["document"].append(current_section)
            else:
                hyperlinks_text = get_hyperlink(paragraph)
                text = paragraph.text
                if hyperlinks_text and len(hyperlinks_text) > 0:
                    text = ''.join(hyperlinks_text)
                    # if current_section is None:
                    #     current_section = {"type": "section", "title": "hyperlink_sec", "content": []}
                    # current_section["content"].append({"type": "hyperlinks", "value": link})

                if current_section is not None and text != "":
                    punctuations = '!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~，。！？【】（）《》「」『』“”‘’；：…·'

                    # 检查字符串是否包含标点符号
                    has_punctuation = any(char in punctuations for char in text)
                    if has_punctuation:
                        current_section["content"].append({"type": "text", "value": text})
                    else:
                        # current_section["content"].append({"type": "theme", "value": text})
                        current_section["content"].append({"type": "text", "value": text})

        elif isinstance(block, Table) or 'Table' in block.style.name:
            if isinstance(block, Paragraph):
                text = block.text
                if current_section is not None and text != "":
                    current_section["content"].append({"type": "table-theme", "value": text})
                continue

            tables = []
            index_row = 0
            for row in block.rows:
                if index_row == 1:
                    tables_0_split = tables[0].split('|')
                    tmp_line = ''
                    for i in range(len(tables_0_split) - 2):
                        tmp_line += '|------'
                    tmp_line += '|'
                    tables.append(tmp_line)

                row_data = []
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        row_data.append(paragraph.text)
                join = "|".join(row_data)
                tables.append("|" + join + "|")
                index_row += 1
                # print("|".join(row_data))

            if tables is not []:
                if current_section is None:
                    current_section = {"type": "section", "title": "tables_sec", "content": []}
                current_section["content"].append({"type": "table", "value": tables})
    return structured_data


import shutil


def save_to_json(structured_data, output_file):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(structured_data, f, ensure_ascii=False, indent=4)


def parse_output(structured_data, min_level=-1):
    parse_data = []
    tmp_section_content_dict = {}
    tmp_section_dict = {}
    tmp_min_heading_level = [0]
    max_heading_level = 0
    for index, line in enumerate(structured_data['document']):
        title_ = line['title']
        # if "代码恶意漏洞分析" in title_:
        #     print(title_)

        if len(structured_data['document']) == 1:
            parse_data.append(line)
            return parse_data

        if title_.startswith('Heading'):
            split = title_.split(' ')
            heading_level = int(split[1])

            if min_level != -1 and heading_level > min_level:
                content = [{"type": "text", "value": title_.split(' ')[2]}] + line['content'][:]
                line = content

            if max_heading_level == 0 or heading_level < max_heading_level:
                max_heading_level = heading_level



            if index == len(structured_data['document']) - 1:
                # 最后一节
                for index_l in range(len(tmp_min_heading_level) - 1, -1, -1):
                    try:
                        level = tmp_min_heading_level[index_l]
                        if level <= heading_level:
                            section_dict_get = tmp_section_dict.get(int(heading_level), [])
                            if isinstance(line, dict):
                                section_dict_get.append(line)
                            elif isinstance(line, list):
                                section_dict_get.extend(line)
                            tmp_section_dict[int(heading_level)] = section_dict_get

                            tmp_section_dict_get = tmp_section_content_dict.get(int(heading_level), [])
                            if isinstance(line, dict):
                                tmp_section_dict_get.append(line)
                            elif isinstance(line, list):
                                tmp_section_dict_get.extend(line)
                            tmp_section_content_dict[int(heading_level)] = tmp_section_dict_get

                        upper_level = tmp_min_heading_level[max(level - 1, 0)]

                        dict_get_list = tmp_section_dict.get(upper_level, [])
                        dict_get = {}
                        if len(dict_get_list) > 0:
                            dict_get = dict_get_list[-1]

                        get = tmp_section_content_dict.get(level, [])
                        if dict_get.get('content', []) == []:
                            if upper_level != 0:
                                dict_get['content'] = get
                            else:
                                dict_get = get
                        else:
                            content_ = dict_get['content']
                            content_.extend(get)
                            dict_get['content'] = content_
                        tmp_section_content_dict[level] = []

                        if upper_level != 0:
                            dict_get_list[-1] = dict_get
                            tmp_section_dict[upper_level] = dict_get_list
                        else:
                            tmp_section_dict[upper_level] = dict_get

                        tmp_section_dict[level] = []
                        try:
                            if level == tmp_min_heading_level[-1]:
                                if level != 0:
                                    tmp_min_heading_level.pop(-1)
                                if tmp_min_heading_level[-1] == max_heading_level or tmp_min_heading_level[-1] == 0:
                                    parse_data.extend(tmp_section_dict[upper_level])
                                    tmp_min_heading_level = [0]
                                    tmp_section_content_dict = {}
                                    tmp_section_dict = {}
                                    break
                            else:
                                print(f'pop {level} error')
                        except Exception as e:
                            print(e)
                            log.error(Log().trace_back_info())

                    except Exception as e:
                        print(e)
                        log.error(Log().trace_back_info())

            # if (min_level == -1 and int(heading_level) < int(tmp_min_heading_level[-1])) \
            #         or (min_level != -1 and int(heading_level) < int(tmp_min_heading_level[-1])):
            if int(heading_level) < int(tmp_min_heading_level[-1]):
                # 如果当前段落标题值小于tmp_min_heading_level栈内最后一个标题，
                # 则持续弹出内容直到当前段落标题值大于栈内最后一个标题值
                # for index, level in enumerate(reversed(tmp_min_heading_level)):  # 这样取index无法反制
                for index_l in range(len(tmp_min_heading_level) - 1, -1, -1):
                    level = tmp_min_heading_level[index_l]

                    if level > heading_level:
                        upper_level = tmp_min_heading_level[max(index_l - 1, 0)]
                        try:
                            dict_get_list = tmp_section_dict.get(upper_level, [])
                            dict_get = {}
                            if len(dict_get_list) > 0:
                                dict_get = dict_get_list[-1]

                            get = tmp_section_content_dict.get(level, [])
                            if dict_get.get('content', []) == []:
                                if upper_level != 0:
                                    dict_get['content'] = get
                                else:
                                    dict_get = get
                            else:
                                content_ = dict_get['content']
                                content_.extend(get)
                                dict_get['content'] = content_
                            tmp_section_content_dict[level] = []

                            if upper_level != 0:
                                dict_get_list[-1] = dict_get
                                tmp_section_dict[upper_level] = dict_get_list
                            else:
                                tmp_section_dict[upper_level] = dict_get

                            tmp_section_dict[level] = []
                        except Exception as e:
                            print(e)
                            log.error(Log().trace_back_info())

                        try:
                            if level == tmp_min_heading_level[-1]:
                                if level != 0:
                                    tmp_min_heading_level.pop(-1)
                                if tmp_min_heading_level[-1] == max_heading_level or tmp_min_heading_level[-1] == 0:
                                    # if tmp_min_heading_level[-1] == max_heading_level and tmp_min_heading_level[-1] == 0:
                                    parse_data.extend(tmp_section_dict[upper_level])
                                    tmp_min_heading_level = [0]
                                    tmp_section_content_dict = {}
                                    tmp_section_dict = {}
                                    # if tmp_min_heading_level[-1] == 0:
                                    #     max_heading_level = 0

                                    if int(heading_level) > int(tmp_min_heading_level[-1]):
                                        tmp_min_heading_level.append(int(heading_level))
                            else:
                                print(f'pop {level} error')
                        except Exception as e:
                            print(e)
                            log.error(Log().trace_back_info())
                section_dict_get = tmp_section_dict.get(int(heading_level), [])
                if isinstance(line, dict):
                    section_dict_get.append(line)
                elif isinstance(line, list):
                    section_dict_get.extend(line)
                tmp_section_dict[int(heading_level)] = section_dict_get

                tmp_section_dict_get = tmp_section_content_dict.get(int(heading_level), [])
                if isinstance(line, dict):
                    tmp_section_dict_get.append(line)
                elif isinstance(line, list):
                    tmp_section_dict_get.extend(line)
                tmp_section_content_dict[int(heading_level)] = tmp_section_dict_get

                continue

            else:  # 如果当前段落标题值大于栈内最后一个标题值，则当前标题值入栈，并压入内容到tmp_section_dict、tmp_section_content_dict
                section_dict_get = tmp_section_dict.get(int(heading_level), [])
                if isinstance(line, dict):
                    section_dict_get.append(line)
                elif isinstance(line, list):
                    section_dict_get.extend(line)
                tmp_section_dict[int(heading_level)] = section_dict_get

                tmp_section_dict_get = tmp_section_content_dict.get(int(heading_level), [])
                if isinstance(line, dict):
                    tmp_section_dict_get.append(line)
                elif isinstance(line, list):
                    tmp_section_dict_get.extend(line)
                tmp_section_content_dict[int(heading_level)] = tmp_section_dict_get
                if int(heading_level) > int(tmp_min_heading_level[-1]):
                    tmp_min_heading_level.append(int(heading_level))
    return parse_data


def docx_chapter_parser(file_path, min_level=-1):
    try:
        newTime = datetime.datetime.now()
        log.info(f"开始解析文档：{file_path}")
        structured_data = extract_docx_content(file_path)
        log.info(f"开始解析文档级联关系：{file_path}")
        parse_data = parse_output(structured_data, min_level)
        log.info(f"开始解析文档parse_data:{len(parse_data)}")
        file_basename = os.path.basename(file_path)
        file_name = os.path.splitext(file_basename)[0]
        log.info(f"开始解析文档chunks：{file_path}")
        parse_data_concate = concate(parse_data, textList=[],
            sentenceTemp=[],
            themeTemp="",
            themeSentenceTemp=[],
            currentHeadList=[],
            realFirstHeadDic={}, file_name='%s.docx' % file_name)
        log.info(f"开始解析文档parse_data_concate:{len(parse_data_concate)}")
        log.info(f"解析文档 cost: {datetime.datetime.now() - newTime}s")
        uploadInfo = {"status": 200, "message": "解析成功", "data": file_basename}
        return uploadInfo, parse_data_concate
    except Exception as e:
        log.error(Log().trace_back_info())
        log.error(f"解析文档：{file_path}失败！！")
        return {"status": 202, "message": "解析文档失败", "data": file_path}, None

def docx2str(filePath):
    try:
        doc = Document(filePath)
        full_text = []

        # 遍历文档中的每个段落
        for para in doc.paragraphs:
            full_text.append(para.text)

        doc_str = "\n".join(full_text)

        log.info(f"{filePath} document has been converted to txt.")
        return {"status": 200, "message": "ok", "data": os.path.basename(filePath)}, doc_str
    except Exception as e:
        log.error(f"An error occurred: {e}")
        return {"status": 200, "message": "ok", "data": os.path.basename(filePath)}, ""


def mainnn():
    base_path = r"E:\program\python\qgpt\doc\625"
    for root, dirs, files in os.walk(base_path):
        for file in files:
            extension_name = os.path.splitext(file)[1]
            if extension_name != ".docx":
                continue
            file_name = os.path.splitext(file)[0]
            print(file_name)
            docx_file = os.path.join(root, file)
            output_file = os.path.join(os.path.join(base_path, 'parsed_data'), '%s.json' % file_name)

            structured_data = extract_docx_content(docx_file)
            # structured_data = json.load(open(output_file, 'r', encoding='utf-8'))
            # save_to_json(structured_data, output_file)

            # parse_data = parse_output(structured_data, 2)
            parse_data = parse_output(structured_data)
            save_to_json(parse_data, output_file)


if __name__ == '__main__':
    # mainnn()

    # '''

    # # base_path = r"E:\program\python\Employee-handbook\data-ngsoc"
    base_path = r"E:\program\python\qgpt\doc"
    # base_path = r"E:\program\python\qgpt_rag\doc\SecGate\防火墙文档word版"
    #
    file_name = '新华社客户端智能信息处理及安全运营--技术需求书'
    docx_file = os.path.join(base_path, '%s.docx' % file_name)
    # # docx_file = r'E:\program\python\Employee-handbook\data-baoxiao\baoxiaoFAQ.docx'
    # ------------------
    output_file = os.path.join(base_path, f'{file_name}.json')
    # structured_data = json.load(open(output_file, 'r', encoding='utf-8'))
    # ------------------

    structured_data = extract_docx_content(docx_file)
    save_to_json(structured_data, output_file)
    #
    parse_data = parse_output(structured_data)
    save_to_json(parse_data, os.path.join(base_path, f'{file_name}_parse_data.json'))

    parse_data_concate = concate(parse_data, file_name='%s.docx' % file_name)
    json.dump(parse_data_concate, open(os.path.join(base_path, f'{file_name}_data.json'), "w", encoding="utf8"),
              ensure_ascii=False, indent=4)
    # '''
