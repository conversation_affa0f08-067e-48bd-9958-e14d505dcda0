from src.config.constant import log
from src.config.sys_config import BASE_PATH
from transformers import AutoModel, AutoTokenizer
import asyncio
from typing import List, Optional

tokenizer = AutoTokenizer.from_pretrained(f"{BASE_PATH}/src/calculate/embedding_model")
model = AutoModel.from_pretrained(f"{BASE_PATH}/src/calculate/embedding_model")

device = 'cpu'
model.to(device)

log.info("【向量化模型】:加载成功")


def getContentVec(contentList):
    '''
        将文档片段转为向量
    :param contentList: 文档片段的list
    :return: n*768维度的矩阵
    '''
    inputs = tokenizer(contentList, padding=True, truncation=True, max_length=512, return_tensors="pt")
    inputs_on_device = {k: v.to(device) for k, v in inputs.items()}
    outputs = model(**inputs_on_device, return_dict=True)
    embeddings = outputs.last_hidden_state[:, 0]  # cls pooler
    embeddings = embeddings / embeddings.norm(dim=1, keepdim=True)  # normalize
    embedding = embeddings.cpu().detach().numpy().tolist()
    return embedding

def getQueryVec(query):
    '''
        将用户问题转换为向量
    :param query: 用户问题
    :return: 768维向量
    '''
    return getContentVec([query])[0]

async def getContentVecAsync(texts: List[str]) -> List[List[float]]:
    """
    异步获取文本的向量表示
    
    参数:
        texts: 文本列表
        
    返回:
        向量表示列表
    """
    # 使用线程池包装同步方法
    return await asyncio.to_thread(getContentVec, texts)

async def getQueryVecAsync(query: str) -> List[float]:
    """
    异步获取查询的向量表示
    
    参数:
        query: 查询文本
        
    返回:
        向量表示
    """
    vectors = await getContentVecAsync([query])
    return vectors[0]


async def test_async_embedding():
    """测试异步嵌入功能"""
    texts = ["这是一个测试文本", "这是另一个测试文本"]
    
    vectors = await getContentVecAsync(texts)
    print(f"向量维度: {len(vectors[0])}")
    print(f"向量示例: {vectors[0][:5]}...")


if __name__ == "__main__":
    # getContentVec([query])[0]
    v = asyncio.create_task(test_async_embedding())
    asyncio.run(v)
    print(v)