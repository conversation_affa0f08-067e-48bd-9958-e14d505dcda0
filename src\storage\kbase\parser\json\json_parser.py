import json
from src.config.constant import log


class FixedFieldJsonParser:
    def __init__(self, fixed_fields=None):
        """
        初始化解析器，指定固定字段的结构。
        :param fixed_fields: 固定字段的模板
        """
        if fixed_fields is None:
            fixed_fields = {
                "title": "",
                "book_name": "",
                "summary": "",
                "content": ""
            }
        self.fixed_fields = fixed_fields

    def parse(self, filePath=None, fileName=None, raw_json: dict = None, mapping: dict = None,
              merge_content: bool = False):
        """
        解析原始 JSON 数据，并提取指定字段填充到固定字段中。
        :param raw_json: 原始 JSON 数据
        :param mapping: 原始 JSON 字段到固定字段的映射关系
        :param merge_content: 是否将 content 字段合并为一个完整字符串
        :return: 填充后的固定字段字典
        """
        log.info("开始解析json")

        if not raw_json:
            if not filePath:
                return {"code": 202, "message": "路径读取失败", "data": ""}, None

            try:
                raw_json = json.load(open(filePath, "r", encoding="utf8"))
            except:
                log.error("读取json失败")
                return {"code": 202, "message": "文件读取失败", "data": ""}, None

        result = self.fixed_fields.copy()  # 复制固定字段模板

        raw_json = self.clean_json(raw_json)

        if not mapping:

            # 没有map对应关系时，整体raw_json视为content
            result["book_name"] = fileName
            result["content"] = raw_json

        else:
            # 开始解析map对应关系
            log.info(f"mapping 对应关系为：")
            log.info(mapping)
            try:
                for fixed_field, raw_field in mapping.items():
                    if isinstance(raw_field, str):
                        # 如果映射是一个字符串，直接从原始 JSON 中提取值
                        result[fixed_field] = self._get_value_by_path(raw_json, raw_field.split('.'))
                    elif isinstance(raw_field, dict):
                        # 如果映射是一个字典，可能是嵌套结构或拼接逻辑
                        if "join" in raw_field:
                            # 处理拼接逻辑
                            result[fixed_field] = self._join_values(raw_json, raw_field["join"])
                        elif "source" in raw_field:
                            # 处理特殊逻辑，例如从文件名中提取值
                            if raw_field["source"] == "filename":
                                result[fixed_field] = fileName
                            if raw_field["source"] == "rawjson":
                                result[fixed_field] = raw_json
                        else:
                            # 递归处理嵌套结构
                            nested_result = self._extract_nested(raw_json, raw_field)
                            result[fixed_field] = nested_result
            except:
                log.error("解析json失败")
                return {"code": 202, "message": "解析失败", "data": ""}, None

            # 如果需要合并 content 字段
            try:
                if merge_content and "content" in result:
                    result["content"] = self._merge_content(result["content"])
            except:
                log.error("合并 content 字段失败")
                return {"code": 201, "message": "合并 content 字段失败", "data": ""}, None

        return {"code": 200, "message": "解析成功", }, [result]

    def clean_json(self, data):
        """
        递归去除 JSON 数据中值为 null 或为空的项。
        :param data: JSON 数据（字典或列表）
        :return: 清理后的 JSON 数据
        """
        if isinstance(data, dict):
            for key in list(data.keys()):
                value = data[key]
                if value is None or value == "" or value == [] or value == {}:
                    del data[key]
                else:
                    cleaned_value = self.clean_json(value)
                    data[key] = cleaned_value
        elif isinstance(data, list):
            cleaned_list = []
            for item in data:
                if item is not None and item != "" and item != [] and item != {}:
                    cleaned_item = self.clean_json(item)
                    cleaned_list.append(cleaned_item)
            data = cleaned_list
        return data

    def _get_value_by_path(self, data: dict, path: list):
        """
        根据路径从嵌套字典中提取值。
        :param data: 原始数据字典
        :param path: 路径列表，例如 ['book', 'title']
        :return: 提取的值，如果路径不存在则返回空字符串
        """
        current = data
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            elif isinstance(current, list) and key.isdigit() and int(key) < len(current):
                current = current[int(key)]
            else:
                return ""
        return current if current != "" else ""

    def _extract_nested(self, raw_json: dict, nested_mapping: dict) -> dict:
        """
        递归提取嵌套字段。
        :param raw_json: 原始 JSON 数据
        :param nested_mapping: 嵌套字段的映射关系
        :return: 提取后的嵌套字段字典
        """
        nested_result = {}
        for key, value in nested_mapping.items():
            if isinstance(value, str):
                nested_result[key] = self._get_value_by_path(raw_json, value.split('.'))
            elif isinstance(value, dict):
                nested_result[key] = self._extract_nested(raw_json, value)
        return nested_result

    def _join_values(self, raw_json: dict, paths: list) -> str:
        """
        拼接多个路径的值。
        :param raw_json: 原始 JSON 数据
        :param paths: 路径列表，例如 ['book.title', 'book.name']
        :return: 拼接后的字符串
        """
        values = []
        for path in paths:
            value = self._get_value_by_path(raw_json, path.split('.'))
            if value:
                values.append(value)
        return " [AND] ".join(values)

    def _merge_content(self, content: dict) -> str:
        """
        将 content 字段中的所有章节合并为一个完整字符串。
        :param content: content 字段的字典
        :return: 合并后的完整字符串
        """
        merged = []
        for chapter, text in content.items():
            merged.append(str(text))
        return "\n".join(merged)


# 示例用法
if __name__ == "__main__":
    # 原始 JSON 数据
    # raw_json = {
    #     "book": {
    #         "title": "Sample Book",
    #         "name": "The Great Adventure",
    #         "chapters": [
    #             {"chapter_title": "Chapter 1", "text": "This is the first chapter."},
    #             {"chapter_title": "Chapter 2", "text": "This is the second chapter."}
    #         ]
    #     },
    #     "summary": "This book is about an adventure."
    # }

    raw_json = {
        "id": "",
        "vulName": "01ACP 跨站脚本漏洞",
        "cnnvdCode": "CNNVD-202301-552",
        "cveCode": "CVE-2021-4309",
        "publishTime": "2023-01-08 00:00:00",
        "isOfficial": 1,
        "vendor": "1004530",
        "hazardLevel": 3,
        "vulType": "跨站脚本",
        "vulTypeName": "跨站脚本",
        "vulDesc": "01ACP是Michael个人开发者的一个 01-Scripts.de 所有模块的中央管理区。\r\n01-Scripts 01ACP存在跨站脚本漏洞，该漏洞源于未知的处理，操作部分参数会导致跨站脚本。",
        "affectedProduct": "",
        "affectedVendor": "个人开发者",
        "productDesc": "",
        "affectedSystem": "",
        "referUrl": "来源:MISC\r\n链接:https://vuldb.com/?ctiid.217649\r\n\r\n来源:MISC\r\n链接:https://vuldb.com/?id.217649\r\n\r\n来源:MISC\r\n链接:https://github.com/01-Scripts/01ACP/commit/a16eb7da46ed22bc61067c212635394f2571d3c4\r\n\r\n来源:cxsecurity.com\r\n链接:https://cxsecurity.com/cveshow/CVE-2021-4309/\r\n\r\n来源:nvd.nist.gov\r\n链接:https://nvd.nist.gov/vuln/detail/CVE-2021-4309",
        "patchId": "",
        "patch": "https://github.com/01-Scripts/01ACP",
        "deleted": "",
        "version": "",
        "createUid": "",
        "createUname": "",
        "createTime": "",
        "updateUid": "",
        "updateUname": "",
        "updateTime": "2023-10-23 00:00:00",
        "cnnvdFiledShow": "vul_name,cnnvd_code,_code,publish_time,is_official,vendor,hazard_level,vul_type,vul_desc,affected_product,affected_vendor,product_desc,affected_system,refer_url,patch_id,product,update_time,patch",
        "cveVulVO": "",
        "cveFiledShow": "",
        "ibmVulVO": "",
        "ibmFiledShow": "",
        "icsCertVulVO": "",
        "icsCertFiledShow": "",
        "microsoftVulVO": "",
        "microsoftFiledShow": "",
        "huaweiVulVO": "",
        "huaweiFiledShow": "",
        "nvdVulVO": "",
        "nvdFiledShow": "",
        "varchar1": "跨站脚本"
    }

    # 字段映射关系
    # mapping = {
    #     "title": "book.title",
    #     "book_name": "book.name",
    #     "summary": "summary",
    #     "content": {
    #         "chapter1": "book.chapters.0.text",
    #         "chapter2": "book.chapters.1.text"
    #     }
    # }

    # 字段映射关系
    # mapping = {
    #     "title": {"join": ["book.title", "book.name"]},
    #     "book_name": {"source": "filename"},
    #     "summary": "summary",
    #     "content": {
    #         "chapter1": "book.chapters.0.text",
    #         "chapter2": "book.chapters.1.text"
    #     }
    # }

    mapping = {
        "title": "vulName",
        "book_name": {
            "source": "filename"
        },
        "summary": {
            "join": [
                "cveCode",
                "cnnvdCode"
            ]
        },
        "content": {
            "source": "rawjson"
        }
    }

    # 创建解析器实例
    parser = FixedFieldJsonParser()

    # 解析原始 JSON 数据，不合并 content
    info, result_without_merge = parser.parse(raw_json=raw_json, mapping=mapping, merge_content=False)
    print("Without merging content:")
    print(json.dumps(result_without_merge, indent=4, ensure_ascii=False))

    # 解析原始 JSON 数据，合并 content
    info, result_with_merge = parser.parse(raw_json=raw_json, mapping=mapping, merge_content=True)
    print("\nWith merging content:")
    print(json.dumps(result_with_merge, indent=4, ensure_ascii=False))
    print(len(result_with_merge))
