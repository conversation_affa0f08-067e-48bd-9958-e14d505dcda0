# -*- coding: utf-8 -*-
"""
Create Time: 2022/11/15 16:46
Author: liuyigong
"""
import os
import re




def is_empty(string):
    return string == '' or string is None



def longestCommonSubstr(word1: str, word2: str):
    '''
    最长公共子串 动态规划法
    :param word1:
    :param word2:
    :return:
    '''
    m = len(word1)
    n = len(word2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    # dp[i][j]代表word1以i结尾,word2以j结尾，的最大公共子串的长度

    max_len = 0
    row = 0
    col = 0
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if word1[i - 1] == word2[j - 1]:
                dp[i][j] = dp[i - 1][j - 1] + 1
                if max_len < dp[i][j]:
                    max_len = dp[i][j]
                    row = i
                    col = j

    max_str = ""
    i = row
    j = col
    while i > 0 and j > 0:
        if dp[i][j] == 0:
            break
        i -= 1
        j -= 1
        max_str += word1[i]

    lcstr = max_str[::-1]
    # 回溯的得到的最长公共子串
    # print(lcstr)
    return lcstr

def space_efficient_lcs(str_a, str_b):
  """
  longest common subsequence of str_a and str_b, with O(n) space complexity
  """
  if len(str_a) == 0 or len(str_b) == 0:
    return 0
  dp = [0 for _ in range(len(str_b) + 1)]
  for i in range(1, len(str_a) + 1):
    left_up = 0
    dp[0] = 0
    for j in range(1, len(str_b) + 1):
      left = dp[j-1]
      up = dp[j]
      if str_a[i-1] == str_b[j-1]:
        dp[j] = left_up + 1
      else:
        dp[j] = max([left, up])
      left_up = up
  # print(dp[len(str_b)])
  return dp[len(str_b)]

def bottom_up_dp_lcs(str_a, str_b):
  """
  longest common subsequence of str_a and str_b
  """
  if len(str_a) == 0 or len(str_b) == 0:
    return 0
  dp = [[0 for _ in range(len(str_b) + 1)] for _ in range(len(str_a) + 1)]
  for i in range(1, len(str_a) + 1):
    for j in range(1, len(str_b) + 1):
      if str_a[i-1] == str_b[j-1]:
        dp[i][j] = dp[i-1][j-1] + 1
      else:
        dp[i][j] = max([dp[i-1][j], dp[i][j-1]])
  # print("length of LCS is :",dp[len(str_a)][len(str_b)])
  # 输出最长公共子序列
  i, j = len(str_a), len(str_b)
  LCS = ""
  while i > 0 and j > 0:
    if str_a[i-1] == str_b[j-1] and dp[i][j] == dp[i-1][j-1] + 1:   # 这里一定要比较a[i-1]和b[j-1]是否相等
      LCS = str_a[i - 1] + LCS
      i, j = i-1, j-1
      continue
    if dp[i][j] == dp[i-1][j]:
      i, j = i-1, j
      continue
    if dp[i][j] == dp[i][j-1]:
      i, j = i, j-1
      continue
  # print ("LCS is :", LCS)
  return LCS

# bottom_up_dp_lcs('小猫爱吃鱼', '小马想吃草')
# print(longestCommonSubstr('非因工作安排饮酒后工作的', '非工作时间喝酒可以吗'))
# longest_common_substr = longestCommonSubstr('非因工作安排饮酒后工作的', '非工作时间喝酒可以吗')
# print(longest_common_substr)
# substr_index = str('非因工作安排饮酒后工作的').index(longest_common_substr)
# print(substr_index)
# min_index = max(0, substr_index - 4)
# if min_index == 0:
#     print(f'没有关键字词')


_MAPPING = (u'零', u'一', u'二', u'三', u'四', u'五', u'六', u'七', u'八', u'九', u'十', u'十一', u'十二',
 u'十三', u'十四', u'十五', u'十六', u'十七',u'十八', u'十九')
_P0 = (u'', u'十', u'百', u'千',)
_S4 = 10 ** 4
def digit_to_chinese(num):
    if num.isdigit():
        num = int(num)
    assert (0 <= num and num < _S4)
    if num < 20:
        return _MAPPING[num]
    else:
        lst = []
        while num >= 10:
            lst.append(int(num % 10))
            num = num / 10
        lst.append(num)
        c = len(lst)  # 位数
        result = u''
        for idx, val in enumerate(lst):
            val = int(val)
            if val != 0:
                result += _P0[idx] + _MAPPING[val]
                if idx < c - 1 and lst[idx + 1] == 0:
                    result += u'零'
        return result[::-1]

def digit2chinese(query:str):
    if query is None or not query.isdigit():
        return ""
    else:
        compilex = re.compile(r"\d+")
        num_result = compilex.findall(query)
        for num in num_result:
            index = query.index(num)
            num_to_chinese = digit_to_chinese(num)
            query = query.replace(num, num_to_chinese, 1)
    return query

def replace_start_number(content):
    numbers = re.findall(r'\d+', content)

    if not numbers:
        # print("字符串中不存在数字")
        pass
    else:
        # 打印所有找到的数字
        # print("所有数字为：", numbers)

        # 找到最先出现的数字
        first_number = numbers[0]
        # print("最先出现的数字为：", first_number)
        content = re.sub(numbers[0], '', content, count=1)
    if content.startswith(')'):
        content = re.sub('\)', '', content, count=1)
    if content.startswith('）'):
        content = re.sub('）', '', content, count=1)
    return content


def match_sentent_from_paragraph(query, paragraph):
    # 将段落拆分为句子
    # 将下列字符串中的分隔符视为句子的结尾，可以根据需求添加或删除
    END_MARKERS = r'[。！？!?；;\n]'

    # 在正则表达式中使用 Unicode 模式，表示支持 Unicode 字符
    pattern = re.compile(rf"(?u)(?<=\w)(?:{END_MARKERS})\s*")

    # 使用正则表达式进行拆分，得到句子数组
    sentences = pattern.split(paragraph)
    # print(sentences)
    # 遍历每个句子，检查是否包含查询字符串
    for sentence in sentences:
        if query.lower() in sentence.lower():
            return sentence

    # 如果查询字符串没有在段落中找到，则返回 None
    return None

def is_chinese_or_number(s):
    for c in s:
        if not (c.isnumeric() or c.isascii()):
            return False
    return True


def deleteP(s):
    punctuation = '！!,;:?\'、，；。：*&^%$#@~`-_—[]{}【】|<>《》？（）()“”"'
    text = s
    for i in punctuation:
        text = text.replace(i, '')
    # text = re.sub(r'[{}]+'.format(punctuation),' ',s)
    return len(text.replace(' ', '')) <= 1, text
    pass


def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass

    try:
        import unicodedata
        unicodedata.numeric(s)
        return True
    except (TypeError, ValueError):
        pass

    return False


def contains_chinese(text):
    '''
    判断一个字符串是否包含中文字符
    '''
    pattern = re.compile(r'[\u4e00-\u9fa5]')
    match = pattern.search(text)
    return match is not None


def is_english(text):
    english_pattern = re.compile(r'\b[a-zA-Z]+\b')
    if english_pattern == deleteP(text):
        return True
    else:
        return False


def retain_chinese(text):
    '''
    只保留中文，其他字符都去除。\u4e00-\u9fff代表Unicode字符集中的汉字区域，该正则表达式 pattern 只会匹配这些区域内的字符。
    []+ 是重复匹配某个字符类至少一次，这里也就是匹配直至遇到非汉字字符便停止（懒惰匹配），因为非汉字字符不会被此正则表达式匹配
    :param text:
    :return:
    '''
    regex_pattern = r'[\u4e00-\u9fff]+'
    matches = re.findall(regex_pattern, text)
    # print(matches)
    return matches


def retain_ch_eg(text):
    # 匹配所有中文字符的正则表达式
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')

    # 匹配不带特殊符号或标点的纯英文单词的正则表达式
    english_pattern = re.compile(r'\b[a-zA-Z]+\b')

    # 提取所有中文字符和纯英文单词
    chinese_matches = chinese_pattern.findall(text)
    chinese_matches = ''.join(chinese_matches)
    english_matches = english_pattern.findall(text)
    english_matches = ''.join(english_matches)

    # 匹配中文字符和纯英文单词的正则表达式
    pattern = re.compile(r'[\u4e00-\u9fffa-zA-Z]+')

    # 提取中文字符和纯英文单词
    matches = pattern.findall(text)

    # 将中文字符和英文单词按照文本中出现的位置重新组合成一个字符串
    result = ''.join(matches)

    # print(result)
    return result, chinese_matches, english_matches

def secure_filename(filename):
    '''
        过滤文件名为安全的文件名
    :param filename: 原文件名
    :return:安全解析后的文件名
    '''
    from unicodedata import normalize
    filename = normalize('NFKD', filename).encode('utf-8', 'ignore')  # 转码
    filename = filename.decode('utf-8')  # 解码

    for sep in os.sep, os.path.altsep:
        if sep:
            filename = filename.replace(sep, " ")

    # 正则增加对汉字的过滤	\u4E00-\u9FBF	中文
    # 自定义构建新正则
    _filename_ascii_add_strip_re = re.compile(r'[^A-Za-z0-9_\u4E00-\u9FBF.-]')

    # 使用正则
    # 根据文件名中的空字符，包括空格、换行(\n)、制表符(\t)等，把文件名分割成列表，然后使用下划线“_”进行连接，再过滤掉正则之外的字符，最后去掉字符串两头的“._”字符，最终生成新的文件名
    filename = str(_filename_ascii_add_strip_re.sub('', '_'.join(filename.split()))).strip('._')

    _windows_device_files = {
        "CON",
        "PRN",
        "AUX",
        "NUL",
        *(f"COM{i}" for i in range(10)),
        *(f"LPT{i}" for i in range(10)),
    }

    if (
        os.name == "nt"
        and filename
        and filename.split(".")[0].upper() in _windows_device_files
    ):
        filename = f"_{filename}"

    return filename

if __name__ == '__main__':

    # print(digit2chinese('国家保密法 第105条'))
    print('。'.lower())
    paragraph = "Python是一种面向对象、解释型的计算机编程语言。它既简单又强大，被广泛用于Web开发、数据分析和人工智能等领域。"
    query = "Web开发"

    # print(match_sentent_from_paragraph(query, paragraph))
    longest_common_substr = longestCommonSubstr('病毒防护策略检测引擎', '病毒防护策略的病毒防护信息')
    print(longest_common_substr)