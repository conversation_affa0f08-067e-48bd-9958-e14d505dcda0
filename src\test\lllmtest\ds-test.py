from openai import OpenAI
import time
client = OpenAI(api_key="sk-be9129d0f72e4c6f911b6479b3460fbc", base_url="https://api.deepseek.com")

# Round 1
print(time.time())
messages = [{"role": "user", "content": "9.11 and 9.8, which is greater?"}]
response = client.chat.completions.create(
    model="deepseek-chat",
    messages=messages,
    stream=True
)
reasoning_content = ""
content = ""

for chunk in response:
    print(time.time())
    # if chunk.choices[0].delta.reasoning_content:
    #     reasoning_content += chunk.choices[0].delta.reasoning_content
    # else:
    if chunk.choices[0].delta.content:
        content += chunk.choices[0].delta.content


print(reasoning_content)
print(content)
#
# # Round 2
# messages.append({"role": "assistant", "content": content})
# messages.append({'role': 'user', 'content': "中国的国土面积有多少"})
# response = client.chat.completions.create(
#     model="deepseek-reasoner",
#     messages=messages,
#     stream=True
# )
# # ...