# -*- coding: utf-8 -*-
"""
Create Time: 2024/7/18 11:28
Author: liuyigong
"""
from docx import Document
import comtypes.client
import os

def doc_to_docx(doc_path, docx_path):
    # 打开现有的.doc文件
    try:
        doc = comtypes.client.CreateObject('Word.Document')
        doc.Load(doc_path)
    except Exception as e:
        print(f"无法加载.doc文件: {e}")
        return

    # 创建一个新的.docx文档
    try:
        new_doc = Document()
    except Exception as e:
        print(f"无法创建.docx文件: {e}")
        return

    # 遍历.doc文件中的每个段落并复制到.docx文档中
    for para in doc.Paragraphs:
        new_para = new_doc.add_paragraph(para.Text)
        # 复制段落的格式（可选）
        new_para.alignment = para.Alignment
        new_para.font.size = para.Range.Font.Size

    # 保存新的.docx文档
    try:
        new_doc.save(docx_path)
        print(f"文档已保存为: {docx_path}")
    except Exception as e:
        print(f"无法保存.docx文件: {e}")

# 调用函数
doc_path = 'example.doc'  # 你的.doc文件路径
docx_path = 'example.docx'  # 输出的.docx文件路径
doc_to_docx(doc_path, docx_path)



# ------------------------------------


def doc_to_docx2(doc_path, docx_path):
    # 读取.doc文件
    doc = Document(doc_path)
    new_doc = Document()

    # 复制内容和格式
    for element in doc.element.body:
        new_doc.element.body.append(element)

    # 保存为.docx文件
    new_doc.save(docx_path)

# 调用函数
doc_path = r'WPS-DOC.doc'  # 你的.doc文件路径
docx_path = 'WPS-DOCexample.docx'  # 输出的.docx文件路径
doc_to_docx2(doc_path, docx_path)