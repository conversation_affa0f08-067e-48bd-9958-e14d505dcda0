from typing import List, Optional, Union
from langchain_core.embeddings import Embeddings
import requests  # 如果走HTTP接口需要


class LocalEmbeddings(Embeddings):
    def __init__(self,
                 endpoint: str = "http://localhost:5000/embed",  # 您的本地接口地址
                 batch_size: int = 32  # 批量处理大小（可选优化）
                 ):
        self.endpoint = endpoint
        self.batch_size = batch_size

    def _get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """实际调用本地嵌入接口的核心方法"""
        # 方式1：HTTP接口调用（根据实际接口调整）
        response = requests.post(
            self.endpoint,
            json={"texts": texts},
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return response.json()["embeddings"]

        # 方式2：直接本地函数调用（若接口是Python函数）
        # return [your_local_embedding_function(text) for text in texts]

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """处理文档列表的嵌入"""
        # 分批处理防止内存溢出
        embeddings = []
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            embeddings.extend(self._get_embeddings(batch))
        return embeddings

    def embed_query(self, text: str) -> List[float]:
        """处理单个查询的嵌入"""
        return self._get_embeddings([text])[0]