import asyncio
import os
import json
from typing import Optional
from contextlib import AsyncExitStack

from openai import OpenAI  
from dotenv import load_dotenv

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 导入共享日志配置
from logger_config import get_logger

# 获取日志记录器
logger = get_logger("client_ser")

# 加载 .env 文件，确保 API Key 受到保护
load_dotenv()

class MCPClient:
    def __init__(self):
        """初始化 MCP 客户端"""
        logger.info("初始化 MCP 客户端")
        self.exit_stack = AsyncExitStack()
        self.openai_api_key = os.getenv("OPENAI_API_KEY")  # 读取 OpenAI API Key
        self.base_url = os.getenv("BASE_URL")  # 读取 BASE URL
        self.model = os.getenv("MODEL")  # 读取 model
        if not self.openai_api_key:
            logger.error("未找到 OpenAI API Key")
            raise ValueError("❌ 未找到 OpenAI API Key，请在 .env 文件中设置 OPENAI_API_KEY")
        logger.info(f"使用模型: {self.model}")
        self.client = OpenAI(api_key=self.openai_api_key, base_url=self.base_url) # 创建OpenAI client
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()        

    async def connect_to_server(self, server_script_path: str):
        """连接到 MCP 服务器并列出可用工具"""
        logger.info(f"正在连接到服务器: {server_script_path}")
        is_python = server_script_path.endswith('.py')
        is_js = server_script_path.endswith('.js')
        if not (is_python or is_js):
            logger.error(f"不支持的服务器脚本类型: {server_script_path}")
            raise ValueError("服务器脚本必须是 .py 或 .js 文件")

        command = "python" if is_python else "node"
        
        # 设置环境变量，强制使用 UTF-8 编码
        env = os.environ.copy()
        env["PYTHONIOENCODING"] = "utf-8"
        
        server_params = StdioServerParameters(
            command=command,
            args=[server_script_path],
            env=env  # 传递修改后的环境变量
        )

        # 启动 MCP 服务器并建立通信
        logger.info("启动 MCP 服务器并建立通信")
        try:
            stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
            self.stdio, self.write = stdio_transport
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))

            await self.session.initialize()

            # 列出 MCP 服务器上的工具
            response = await self.session.list_tools()
            tools = response.tools
            tool_names = [tool.name for tool in tools]
            
            # 打印每个工具的详细信息，包括输入模式
            for tool in tools:
                logger.info(f"工具名称: {tool.name}")
                logger.info(f"工具描述: {tool.description}")
                logger.info(f"工具输入模式: {tool.inputSchema}")
            
            logger.info(f"已连接到服务器，支持以下工具: {tool_names}")
            print("\n已连接到服务器，支持以下工具:", tool_names)
        except UnicodeDecodeError as e:
            logger.error(f"编码错误: {str(e)}")
            raise ValueError(f"连接服务器时出现编码错误，请确保所有输出都是 UTF-8 编码: {str(e)}")
        except Exception as e:
            logger.error(f"连接服务器时出错: {str(e)}", exc_info=True)
            raise ValueError(f"连接服务器时出错: {str(e)}")
        
    async def process_query(self, query: str) -> str:
        """
        使用大模型处理查询并调用可用的 MCP 工具 (Function Calling)
        """
        logger.info(f"处理用户查询: {query}")
        messages = [{"role": "user", "content": query}]
        
        try:
            # 获取可用工具列表
            response = await self.session.list_tools()
            
            available_tools = [{
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema
                }
            } for tool in response.tools]
            logger.debug(f"可用工具: {available_tools}")
            
            logger.info("调用 OpenAI API")
            response = self.client.chat.completions.create(
                model=self.model,            
                messages=messages,
                tools=available_tools     
            )
            
            # 处理返回的内容
            content = response.choices[0]
            logger.debug(f"API 响应: {content}")
            
            if content.message.tool_calls and content.finish_reason == "tool_calls":
                # 如果需要使用工具，就解析工具
                tool_call = content.message.tool_calls[0]
                tool_name = tool_call.function.name
                tool_args = json.loads(tool_call.function.arguments)
                
                logger.info(f"调用工具: {tool_name}, 参数: {tool_args}")
                print(f"\n\n[Calling tool {tool_name} with args {tool_args}]\n\n")
                
                # 执行工具
                try:
                    # 确保工具参数格式正确
                    logger.debug(f"调用工具前的参数: {tool_args}")
                    
                    # 执行工具调用
                    result = await self.session.call_tool(tool_name, tool_args)
                    logger.info(f"工具执行结果: {result}")
                    
                    # 将模型返回的调用哪个工具数据和工具执行完成后的数据都存入messages中
                    messages.append({"role": "assistant", "content": None, "tool_calls": [
                        {
                            "id": tool_call.id,
                            "type": "function",
                            "function": {
                                "name": tool_name,
                                "arguments": json.dumps(tool_args)
                            }
                        }
                    ]})
                    
                    # 确保结果内容存在
                    tool_result = result.content[0].text if result.content and len(result.content) > 0 else "工具执行失败，未返回结果"
                    
                    messages.append({
                        "role": "tool",
                        "content": tool_result,
                        "tool_call_id": tool_call.id,
                    })
                    
                    logger.info("生成最终回复")
                    # 将上面的结果再返回给大模型用于生产最终的结果
                    final_response = self.client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                    )
                    return final_response.choices[0].message.content
                except Exception as tool_error:
                    logger.error(f"工具调用失败: {str(tool_error)}", exc_info=True)
                    return f"⚠️ 工具调用失败: {str(tool_error)}"
            
            return content.message.content
        except Exception as e:
            logger.error(f"处理查询时出错: {str(e)}", exc_info=True)
            if "404" in str(e) and "No endpoints found that support tool use" in str(e):
                logger.warning("当前模型不支持工具调用，切换到普通对话模式")
                try:
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=[{"role": "user", "content": f"我想问: {query}。注意：我无法为你提供工具调用能力，请直接回答。"}],
                    )
                    return response.choices[0].message.content
                except Exception as e2:
                    logger.error(f"普通对话模式也失败: {str(e2)}", exc_info=True)
                    return f"⚠️ 发生错误: {str(e2)}"
            return f"⚠️ 发生错误: {str(e)}"
    
    async def chat_loop(self):
        """运行交互式聊天循环"""
        logger.info("启动聊天循环")
        print("\n🤖 MCP 客户端已启动！输入 'quit' 退出")

        while True:
            try:
                query = input("\n你: ").strip()
                if query.lower() == 'quit':
                    logger.info("用户退出聊天")
                    break
                
                logger.info(f"用户输入: {query}")
                response = await self.process_query(query)  # 发送用户输入到 OpenAI API
                logger.info(f"AI 回复: {response}")
                print(f"\n🤖 OpenAI: {response}")

            except Exception as e:
                logger.error(f"聊天循环中发生错误: {str(e)}", exc_info=True)
                print(f"\n⚠️ 发生错误: {str(e)}")

    async def cleanup(self):
        """清理资源"""
        logger.info("清理资源")
        await self.exit_stack.aclose()

async def main():
    logger.info("程序启动")
    # if len(sys.argv) < 2:
    #     print("Usage: python client.py <path_to_server_script>")
    #     sys.exit(1)

    client = MCPClient()
    try:
        # await client.connect_to_server(sys.argv[1])
        await client.connect_to_server("weather_server.py")
        await client.chat_loop()
    except Exception as e:
        logger.error(f"主程序异常: {str(e)}", exc_info=True)
        print(f"程序异常: {str(e)}")
    finally:
        await client.cleanup()
        logger.info("程序结束")

if __name__ == "__main__":
    import sys
    asyncio.run(main())
