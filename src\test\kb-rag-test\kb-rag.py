import datetime
from flask import Flask, request, send_from_directory, jsonify
import json
import requests
from flask_cors import CORS
app = Flask(__name__)
CORS(app)
app.config['JSON_AS_ASCII'] = False  # 页面直接显示中文
app.config['DEBUG'] = False

prompt = '''
###职位描述
您是一名专业的问答助理，负责根据提供的知识信息回答用户的问题。遵循以下准则以确保一致性和准确性。
###任务
您需要根据<context>中的所有信息全面回答用户在提出的问题,用户的问题在<question>中。
步骤：
1.查看<context>标签中提供的上下文知识。
2.输出中不要包含任何XML标记。
3.以正常语言输出答案，不需要结构标记。
###记忆
“在<context></context>XML标签中使用以下上下文作为你学到的知识。
<context>
{{#execute_result#}}
</context>
当回答用户时：
-如果你不知道，就说你不知道。
-如果你不知道什么时候不确定，请要求澄清。
避免提及你是从上下文中获得信息的。
并根据用户问题的语言进行回答。
场景意图是：阅读格式化数据，并回答用户的提问问题。
<question>
用户提问：{{#user_query#}}
</question>
'''


class KbaseService:
    def __init__(self, kbase_url, prompt):
        self.kbase_url = kbase_url
        self.prompt = prompt

    def search(self, query):
        payload = json.dumps({
            "keyword": f"{query}",
            "synonymKeyword": [],
            "knowlCategory": [],
            "highLight": "true",
            "highLightSize": "200",
            "depthPage": "false",
            "sortType": "",
            "sortOrder": "",
            "pageNum": 1,
            "pageSize": 5,
            "complexCondition": [],
            "removeFields": []
        })
        headers = {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        }

        response = requests.request("POST", self.kbase_url, headers=headers, data=payload)

        return self.extract_titles_contents(response.text)

    @staticmethod
    def extract_titles_contents(response_text):
        data = json.loads(response_text)

        search_results = data['data']['search_result']

        results = []

        for item in search_results:
            entry = {
                "title": item.get("title", ""),
                "content": item.get("content", ""),
                "tag": '|'.join(item.get("tag", []))
            }
            results.append(entry)

        return results

    def call_opneapi_chat_completions(self, prompt):
        claude_api = "https://47.92.194.66:8088/v1/chat/completions"
        claude_key = "sk-Fjq1BRbKySR0A2O70120453882D64aEcA736748a2265E6Ff"
        claude_model = "claude-3-5-sonnet-20241022"

        url = claude_api
        api_key = claude_key
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        data = {
            "model": claude_model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 8000,
            "temperature": 0,
            "top_p": 0.9,
            "n": 1
        }

        try:
            response = requests.post(url, json=data, headers=headers, verify=False)
            response.raise_for_status()
            result = response.json()
            reply = result['choices'][0]['message']['content']
            print(f"reply:{reply}")
            return reply
        except requests.RequestException as e:
            print(f"err:{e}")
        except KeyError as e:
            print(f"erroe:{e}")
        return

    def getLlmAnswer(self, prompt, llm_url="https://10.51.106.17:11066/chat/api/v1/gpt-stream"):
        print(prompt)
        headers = {
            'Content-Type': 'text/plain'
        }
        response = requests.request("POST", llm_url, headers=headers, data=prompt.encode("utf8"), verify=False)
        print(f"response.text:{response.text}")
        return response.text

    def ask(self, query, model):
        search = self.search(query=query)
        prompt_re = self.prompt.replace("{{#execute_result#}}", json.dumps(search, ensure_ascii=False))
        prompt_re = prompt_re.replace("{{#user_query#}}", query)
        answer = ''
        try:
            if model == 'qgpt':
                answer = self.getLlmAnswer(prompt_re)
            elif model == 'claude':
                answer = self.call_opneapi_chat_completions(prompt_re)
        except:
            answer = 'wrong'
        return answer, search


kbaseService = KbaseService("http://10.49.173.106:49036/search", prompt)

@app.route("/")
def index():
    return send_from_directory("..", "index.html")

@app.route("/kbase/rag/ask", methods=["POST"])
def KbaseCreate():
    print('--------------------------')
    if request.method != 'POST':
        upload = {"status": 202, "message": "错误", "data": {}}
        return json.dumps(upload, ensure_ascii=False)
    newTime = datetime.datetime.now()
    print(request.json)
    query = request.json.get("query", "")
    print(query)
    model = request.json.get("model", "qgpt")
    print(model)
    ask, search = kbaseService.ask(query, model)
    print(f"answer cost: {datetime.datetime.now() - newTime}s")
    return jsonify({"status": 200, "message": "OK", "data": {"ask": ask, "resources": search}})



if __name__ == '__main__':
    app.run(port=7077, host='0.0.0.0', debug=False)