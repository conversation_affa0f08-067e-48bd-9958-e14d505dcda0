from typing import List
from langchain_core.embeddings import Embeddings
from src.config.constant import log
import torch
from transformers import AutoModel, AutoTokenizer
class LocalHFEmbeddings(Embeddings):
    def __init__(self,
                 model: AutoModel,
                 tokenizer: AutoTokenizer,
                 device: str = 'cpu',
                 normalize: bool = True):
        """
        参数说明：
        - model: 已加载的HF模型实例
        - tokenizer: 已加载的HF分词器实例
        - device: 计算设备 ('cpu'/'cuda')
        - normalize: 是否对向量做归一化
        """
        self.model = model
        self.tokenizer = tokenizer
        self.device = device
        self.normalize = normalize
        self.model.to(self.device)
        log.info("【本地向量化服务】初始化完成")

    def _embed(self, texts: List[str]) -> List[List[float]]:
        """核心向量化方法"""
        # 分词与设备转移
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors="pt"
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}

        # 前向计算
        with torch.no_grad():
            outputs = self.model(**inputs, return_dict=True)

        # 取CLS Token的隐藏状态
        embeddings = outputs.last_hidden_state[:, 0]  # shape: (batch_size, hidden_size)

        # 归一化处理
        if self.normalize:
            embeddings = embeddings / embeddings.norm(dim=1, keepdim=True)

        # 转为Python列表
        return embeddings.cpu().numpy().tolist()

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        return self._embed(texts)

    def embed_query(self, text: str) -> List[float]:
        return self._embed([text])[0]