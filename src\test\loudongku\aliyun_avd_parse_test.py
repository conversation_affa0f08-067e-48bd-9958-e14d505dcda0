from bs4 import BeautifulSoup
import os
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed


def extract_vulnerability_info(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')

    # 提取漏洞编号
    vulnerability_id = None
    for li in soup.find_all('li', class_='breadcrumbs__list-item-last'):
        if 'AVD' in li.text:
            vulnerability_id = li.text.strip()
            break

    # 提取漏洞名称
    vulnerability_name = None
    header_title = soup.find('span', class_='header__title__text')
    if header_title:
        vulnerability_name = header_title.text.strip()

    # 提取漏洞描述
    vulnerability_description = None
    description_section = soup.find('h6', string='漏洞描述')
    if description_section:
        description_div = description_section.find_next('div', class_='text-detail')
        if description_div:
            vulnerability_description = description_div.text.strip()

    # 提取漏洞修复方案
    fix_recommendation = None
    fix_section = soup.find('h6', string='解决建议')
    if fix_section:
        fix_div = fix_section.find_next('div', class_='text-detail')
        if fix_div:
            fix_recommendation = fix_div.text.strip()

    # 提取CVE编号
    cve_id = None
    cve_section = soup.find(text=re.compile(r'CVE编号'))
    if cve_section:
        cve_id = cve_section.find_next('div', class_='metric-value').text.strip()

    # 提取CNNVD编号
    cnnvd_id = None
    cnnvd_section = soup.find(text=re.compile(r'CNNVD编号'))
    if cnnvd_section:
        cnnvd_id = cnnvd_section.find_next('div', class_='metric-value').text.strip()

    return {
        '漏洞编号': vulnerability_id,
        '漏洞名称': vulnerability_name,
        '漏洞描述': vulnerability_description,
        '漏洞修复方案': fix_recommendation,
        'CVE编号': cve_id,
        'CNNVD编号': cnnvd_id
    }


def process_file(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            html_content = file.read()
            result = extract_vulnerability_info(html_content)
            return result
    except Exception as e:
        print(f"处理文件 {filepath} 时出错: {e}")
        return None


def process_html_files_multithreaded(directory, max_workers=None):
    results = []
    files = [os.path.join(directory, filename) for filename in os.listdir(directory) if filename.endswith('.html')]

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(process_file, filepath): filepath for filepath in files}
        for future in as_completed(futures):
            result = future.result()
            if result:
                results.append(result)

    return results

import json
def save_to_json(results, output_file):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=4)
    print(f"结果已保存到 {output_file}")

# 使用示例
if __name__ == "__main__":
    directory_path = r"/doc/avd.aliyun"
    # 设置最大线程数，可以根据CPU核心数调整
    max_workers = os.cpu_count()  # 使用CPU核心数作为线程数

    extracted_info = process_html_files_multithreaded(directory_path, max_workers)

    output_file = "vulnerabilities.json"
    save_to_json(extracted_info, output_file)

    # 打印提取的信息
    for info in extracted_info:
        print("漏洞编号:", info['漏洞编号'])
        print("漏洞名称:", info['漏洞名称'])
        print("漏洞描述:", info['漏洞描述'])
        print("漏洞修复方案:", info['漏洞修复方案'])
        print("CVE编号:", info['CVE编号'])
        print("CNNVD编号:", info['CNNVD编号'])
        print("-" * 50)