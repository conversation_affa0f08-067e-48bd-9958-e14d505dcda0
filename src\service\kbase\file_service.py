import os
import json
import traceback
import src.service.firewall.document_service as documentService
from src.utils.string_util import secure_filename
from src.utils.file_util import calculateMd5File
from src.config.constant import kbaseFilePath
from src.config.constant import log
from src.storage.kbase.parser.pdf.pdf_parser import pdfToStr, getPdfDocList
from src.storage.kbase.parser.txt.TextSegment import textToStr
from src.storage.kbase.parser.json.json_parser import FixedFieldJsonParser
from src.storage.kbase.parser.word.docx_parser import docx_chapter_parser, docx2str
import src.storage.kbase.parser.general_length_split as lengthSplite
import src.storage.kbase.parser.general_chapter_split as chapterSplite
from src.storage.kbase.parser.table.table_parser import tableToJson
from src.utils.elasticsearch_util import elasticSearchEngine
from src.utils.milvus_util import milvusClient
from src.config.constant import chunks_max_tokens, chunks_overlap, max_doc_num
from src.config.constant import es_doc_index, milvus_index, es_kbase_index

def initDoc():
    try:
        elasticSearchEngine.create(index_name=es_doc_index)
    except:
        log.error("【文档索引创建出错】")
        log.error(traceback.format_exc())

    try:
        milvusClient.create_collection(collection_name=milvus_index, description="文档向量集合")
    except:
        log.error("【文档向量集合创建出错】")
        log.error(traceback.format_exc())


initDoc()


def isDocInKbase(kbaseId, fileName, filePath):
    '''
    判断文档是否在知识库中
    :param kbaseId: 知识库id
    :param fileName: 文档名
    :return: 存在同名文档返回1，存在同名md5文档返回2， 不存在返回0
    '''

    # 判断文件名是否存在
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "kbase_id": int(kbaseId)
                        }
                    },
                    {
                        "term": {
                            "doc_name_keyword": fileName
                        }
                    }
                ]
            }
        }
    }
    result = elasticSearchEngine.search(query=query, index_name=es_doc_index)
    if result["hits"]["total"]["value"] > 0:
        return 1

    # 判断文件md5是否存在
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "kbase_id": int(kbaseId)
                        }
                    },
                    {
                        "term": {
                            "doc_md5": calculateMd5File(filePath)
                        }
                    }
                ]
            }
        }
    }
    result = elasticSearchEngine.search(query=query, index_name=es_doc_index)
    if result["hits"]["total"]["value"] > 0:
        return 2

    return 0

def getKbaseDocList(kbaseId):
    query = {
        "size": 999,
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "kbase_id": kbaseId
                        }
                    }
                ]
            }
        },
        "collapse": {
            "field": "doc_id"
        },
        "from": 0,
        "track_total_hits": True,
        "aggs": {
            "courseAgg": {
                "cardinality": {
                    "field": "doc_id"
                }
            }
        }
    }
    result = elasticSearchEngine.search(index_name=es_doc_index, query=query)
    log.info(f"【{kbaseId}文档列表查询结果】: {len(result)} 条")
    return result

def chapterSpilteAndInsert(kbaseId, filePath, fileName, lastLevel, parameters):
    '''
        按章节解析文档并保存
    :param kbaseId: 知识库id
    :param filePath: 文档全路径
    :param fileName: 文档名
    :param lastLevel: 解析到的最大章节等级
    :return:
    '''
    # 不同类型文档解析为统一格式
    # [ { "title": "xxxx||xxxx||xxx", "content": "xxxxxxxx" } ]
    docDicList = []

    if fileName.endswith("pdf"):
        uploadInfo, docDicList = getPdfDocList(filePath, fileName, lastLevel)
    elif fileName.endswith("docx"):
        min_level = -1 if lastLevel == 999 else lastLevel
        uploadInfo, docDicList = docx_chapter_parser(filePath, min_level=min_level)

    else:
        uploadInfo = {"status": 202, "message": "暂未支持该类型解析", "data": fileName}

    # 章节分段只有一段说明没有解析到目录
    if len(docDicList) <= 1:
        uploadInfo = {"status": 204, "message": "章节分段解析失败", "data": fileName}

    # 状态码为200说明解析正确，将按照章节切分的进行扩充
    if uploadInfo["status"] == 200 and len(docDicList) != 0:
        # 字段富化，构造入库结构
        esInsertList, milvusInsertList = chapterSplite.generateInsertDic(kbaseId, fileName, filePath, docDicList, parameters)
        # 入库
        documentService.es_insert_data(es_data=esInsertList, index_name=es_doc_index,
                                       milvus_data=milvusInsertList, collection_name=milvus_index)
        uploadInfo = {
            "status": 200,
            "message": "上传成功",
            "data": {
                "doc_id": esInsertList[0]["doc_id"],
                "doc_name": fileName,
                "model": "chapter"
            }
        }
        if lastLevel != 999:
            uploadInfo["last_level"] = lastLevel
    else:
        uploadInfo = {
            "status": 202,
            "message": "文档解析章节为空",
            "data": {
                "doc_name": fileName
            }
        }
    return uploadInfo

def tableSpilteAndInsert(kbaseId, filePath, fileName, parameters):
    '''
        按表格解析文档并保存
    :param kbaseId: 知识库id
    :param filePath: 文档全路径
    :param fileName: 文档名
    :return:
    '''
    info, tableList = tableToJson(filePath, fileName)
    if info.get("code") != 200 or len(tableList) == 0:
        uploadInfo = {"status": 209, "message": "上传失败", "data": fileName}
    else:
        # 字段富化，构造入库结构
        esInsertList, milvusInsertList = chapterSplite.generateInsertDic(kbaseId, fileName, filePath, tableList, parameters)
        # 入库
        documentService.es_insert_data(es_data=esInsertList, index_name=es_doc_index,
                                       milvus_data=milvusInsertList, collection_name=milvus_index)
        uploadInfo = {
            "status": 200,
            "message": "上传成功",
            "data": {
                "doc_id": esInsertList[0]["doc_id"],
                "doc_name": fileName,
                "model": "chapter"
            }
        }
    return uploadInfo


def jsonSpilteAndInsert(kbaseId, filePath, fileName, mapping, parameters, merge_content=True, ):
    '''
        按JSON结构解析文档并保存
    :param kbaseId: 知识库id
    :param filePath: 文档全路径
    :param fileName: 文档名
    :return:
    '''
    # 解析JSON文件
    json_parser = FixedFieldJsonParser()
    info, json_data = json_parser.parse(filePath=filePath, fileName=fileName, mapping=mapping, merge_content=False)

    if info.get("code") != 200 or len(json_data) == 0:
        uploadInfo = {"status": 209, "message": "上传失败", "data": fileName}
    else:
        # 字段富化，构造入库结构
        esInsertList, milvusInsertList = chapterSplite.generateInsertDic(kbaseId, fileName, filePath, json_data, parameters)

        # 入库
        documentService.es_insert_data(es_data=esInsertList, index_name=es_doc_index,
                                       milvus_data=milvusInsertList, collection_name=milvus_index)

        uploadInfo = {
            "status": 200,
            "message": "上传成功",
            "data": {
                "doc_id": esInsertList[0]["doc_id"],
                "doc_name": fileName,
                "model": "json"
            }
        }
    return uploadInfo

def lengthSpilteAndInsert(kbaseId, filePath, fileName, maxTokens, chunkOverlap):
    '''
        按长度解析文档并保存
    :param kbaseId: 知识库id
    :param filePath: 文档全路径
    :param fileName: 文档名
    :param maxTokens: 保留块长度
    :param chunkOverlap: 保留上下文长度
    :return:
    '''
    # 不同类型文档均解析为纯字符串
    docStr = ""
    # 根据不同文档类型解析成纯文本
    if fileName.endswith("pdf"):
        uploadInfo, docStr = pdfToStr(filePath)
    elif fileName.endswith("txt"):
        uploadInfo, docStr = textToStr(filePath)
    elif fileName.endswith("docx"):
        uploadInfo, docStr = docx2str(filePath)
    else:
        uploadInfo = {"status": 202, "message": "暂未支持该类型解析", "data": fileName}
    # 状态码为200说明解析正确，将纯文本根据输入参数分段，并格式化向量化
    if uploadInfo["status"] == 200 and docStr.strip() != "":
        # 字段富化，构造入库结构
        esInsertList, milvusInsertList = lengthSplite.generateInsertDic(kbaseId, fileName, filePath, docStr,
                                                                        maxTokens, chunkOverlap)
        # 入库
        documentService.es_insert_data(es_data=esInsertList, index_name=es_doc_index,
                                       milvus_data=milvusInsertList, collection_name=milvus_index)
        uploadInfo = {
            "status": 200,
            "message": "上传成功",
            "data": {
                "doc_id": esInsertList[0]["doc_id"],
                "doc_name": fileName,
                "model": "length",
                "max_tokens": maxTokens,
                "chunk_overlap": chunkOverlap
            }
        }
    else:
        uploadInfo = {
            "status": 202,
            "message": "文档解析为空",
            "data": {
                "doc_name": fileName
            }
        }
    return uploadInfo


def doUpload(uploadFile, parameters):
    '''
    文件上传和存储
    :param uploadFile: 文件
    :param kbaseId: 知识库id
    :param model: 分段形式（长度或章节）chapter/length
    :param maxTokens: 分段后每段的字数
    :param chunkOverlap: 上下文保留的字数
    :param lastLevel: 章节分隔时最小等级
    :param mapping: json解析对应关系
    :param merge_content: json解析content是否合并
    :return: 上传结果
    '''
    # --------------------参数判断---------------------
    kbaseId = parameters.get("kbaseId")
    model = parameters.get("model")
    maxTokens = parameters.get("maxTokens")
    chunkOverlap = parameters.get("chunkOverlap")
    lastLevel = parameters.get("lastLevel")
    if len(parameters.get("mapping")):
        mapping = json.loads(parameters.get("mapping"))
    else:
        mapping = None
    merge_content = parameters.get("merge_content", True)

    log.info(f"【文档上传参数】: {parameters}")
    if uploadFile is None or uploadFile.filename == "":
        uploadInfo = {"status": 204, "message": "文件未上传", "data": ""}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)
    if kbaseId == 0 or not kbaseId.isdigit():
        uploadInfo = {"status": 204, "message": "未选择知识库", "data": ""}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)
    kbaseId = int(kbaseId)
    if not elasticSearchEngine.find_filed(field="kbase_id", field_value=kbaseId, index_name=es_kbase_index):
        uploadInfo = {"status": 204, "message": "知识库不存在", "data": ""}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)
    # 判断知识库中的文档是否超过数量
    if getKbaseDocList(kbaseId)["aggregations"]["courseAgg"]["value"] >= max_doc_num:
        uploadInfo = {"status": 204, "message": "超过知识库文档最大数量", "data": kbaseId}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)

    if model not in ["chapter", "length", "auto"]:
        uploadInfo = {"status": 204, "message": "未选择分段方式", "data": ""}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)

    if maxTokens.strip() == "":
        maxTokens = chunks_max_tokens
    if not str(maxTokens).isdigit():
        uploadInfo = {"status": 204, "message": "分段长度错误", "data": ""}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)
    maxTokens = int(maxTokens)

    if chunkOverlap.strip() == "":
        chunkOverlap = chunks_overlap
    if not str(chunkOverlap).isdigit():
        uploadInfo = {"status": 204, "message": "上下文长度错误", "data": ""}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)
    chunkOverlap = int(chunkOverlap)
    if chunkOverlap >= maxTokens:
        uploadInfo = {"status": 204, "message": "上下文长度与分段长度不符", "data": ""}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)
    if lastLevel.strip() == "":
        lastLevel = 999
    if not str(lastLevel).isdigit():
        uploadInfo = {"status": 204, "message": "章节最小等级错误", "data": ""}
        log.info(f"【文档上传结果】:{uploadInfo}")
        return json.dumps(uploadInfo, ensure_ascii=False)
    lastLevel = int(lastLevel)

    # ===================================================

    # 转换为安全的文件名称
    fileName = secure_filename(uploadFile.filename)
    log.info(f"【上传文件】: {fileName}")
    # --------------------文件保存部分---------------------
    filePath = os.path.join(kbaseFilePath, str(kbaseId))
    if not os.path.exists(filePath):
        os.makedirs(filePath)
        log.info(f"【知识库文件夹不存在】: {filePath}")
    filePath = os.path.join(filePath, fileName)
    uploadFile.save(filePath)
    log.info(f"【文件保存】: {filePath}")
    # ===================================================
    try:
        isExist = isDocInKbase(kbaseId=kbaseId, fileName=fileName, filePath=filePath)
        if isExist in [1, 2]:
            if isExist == 2:
                if os.path.exists(filePath):
                    os.remove(filePath)
            uploadInfo = {"status": 202, "message": "文档已存在", "data": fileName}
        else:
            #表格单独处理
            if fileName.endswith(".xlsx") or fileName.endswith(".xls") or fileName.endswith(".csv"):
                model = "table"
                parameters['model'] = "table"
            if fileName.endswith(".json"):
                model = "json"
                parameters['model'] = "json"
            # ===================================================
            # -------------------按照规则分隔------------------------
            # -------------------按照章节分隔------------------------
            if model == "chapter":
                uploadInfo = chapterSpilteAndInsert(kbaseId, filePath, fileName, lastLevel, parameters)
            # =======================================================
            # -------------------表格数据处理------------------------
            elif model == "table":
                uploadInfo = tableSpilteAndInsert(kbaseId, filePath, fileName)
            # =======================================================
            # -------------------JSON数据处理------------------------
            elif model == "json":
                uploadInfo = jsonSpilteAndInsert(kbaseId, filePath, fileName, mapping, parameters, merge_content)
            # =======================================================
            # -------------------智能分隔（先尝试章节，失败后再尝试长度）------------------------
            elif model == "auto":
                uploadInfo = chapterSpilteAndInsert(kbaseId, filePath, fileName, lastLevel, parameters)
                if uploadInfo["status"] != 200:
                    log.info(f"【章节分段失败使用长度分段】:{fileName}")
                    uploadInfo = lengthSpilteAndInsert(kbaseId, filePath, fileName, maxTokens, chunkOverlap)
            # =======================================================
            # -------------------按照长度分隔------------------------
            else:
                uploadInfo = lengthSpilteAndInsert(kbaseId, filePath, fileName, maxTokens, chunkOverlap)

        if uploadInfo["status"] != 200:
            if os.path.exists(os.path.join(kbaseFilePath, fileName)):
                os.remove(os.path.join(kbaseFilePath, fileName))
    except:
        uploadInfo = {"status": 201, "message": "上传异常", "data": fileName}
        if os.path.exists(os.path.join(kbaseFilePath, fileName)):
            os.remove(os.path.join(kbaseFilePath, fileName))
        log.error("【文件上传异常】")
        log.error(traceback.format_exc())
    log.info(f"【文档上传结果】:{uploadInfo}")
    return json.dumps(uploadInfo, ensure_ascii=False)


def doList(kbaseId):
    '''
        文件列表返回
    :return: 文件名和ID
    '''
    log.info(f"【文件列表获取参数】:{kbaseId}")
    if kbaseId == 0 or not kbaseId.isdigit():
        listInfo = {"status": 204, "message": "未选择知识库", "data": ""}
        return json.dumps(listInfo, ensure_ascii=False)
    kbaseId = int(kbaseId)
    try:
        # 去重查询文档列表
        result = getKbaseDocList(kbaseId)
        bookInfo = []
        # 解析列表
        for oneBook in result.get("hits", {}).get("hits", []):
            bookInfo.append({
                "docName": oneBook["_source"]["doc_name"],
                "docId": oneBook["_source"]["doc_id"],
                "model": oneBook["_source"]["model"],
                "createTime": oneBook["_source"]["create_time"]
            })
        listInfo = {"status": 200, "message": "", "data": bookInfo}
    except:
        listInfo = {"status": 201, "message": "获取异常", "data": {}}
        log.error("【文件列表获取异常】")
        log.error(traceback.format_exc())
    log.info(f"【文件列表获取结果】:{listInfo}")
    return json.dumps(listInfo, ensure_ascii=False)


def doDelete(docId):
    '''
        根据文档ID删除文档
    :param docId: 文档ID
    :return:
    '''
    log.info(f"【文件删除参数】:{docId}")
    # 文档ID校验
    if docId == 0 or not docId.isdigit():
        delInfo = {"status": 204, "message": "未选择文档", "data": ""}
        log.info(f"【文件删除结果】:{delInfo}")
        return json.dumps(delInfo, ensure_ascii=False)
    docId = int(docId)
    try:
        query = {
            "size": 1,
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "doc_id": docId
                            }
                        }
                    ]
                }
            }
        }
        result = elasticSearchEngine.search(query=query, index_name=es_doc_index)
        log.info(f"【文档信息查询结果】: {result}")
        if result["hits"]["total"]["value"] == 0:
            delInfo = {"status": 204, "message": "文档不存在", "data": ""}
            log.info(f"【文件删除结果】:{delInfo}")
            return delInfo
        fileName = result["hits"]["hits"][0]["_source"]["doc_name"]
        kbaseId = result["hits"]["hits"][0]["_source"]["kbase_id"]
        # 删除milvus中的文档数据
        documentService.milvus_delete_by_field(collection_name=milvus_index, field="doc_id", field_value=docId)
        # 删除es中的文档数据
        documentService.es_delete_by_field(index_name=es_doc_index, field="doc_id", field_value=docId)
        documentService.es_data_refresh_manually(index_name=es_doc_index)
        filePath = os.path.join(kbaseFilePath, str(kbaseId))
        if os.path.exists(filePath):
            filePath = os.path.join(filePath, fileName)
            if os.path.exists(filePath):
                os.remove(filePath)
                log.info(f"【文档已删除不存在】: {filePath}")
        delInfo = {"status": 200, "message": "删除成功", "data": {}}
    except:
        delInfo = {"status": 201, "message": "删除异常", "data": {}}
        log.error(f"【文件删除异常】:{docId}")
        log.error(traceback.format_exc())
    log.info(f"【文件删除结果】:{delInfo}")
    return json.dumps(delInfo, ensure_ascii=False)
