from src.utils.sensitive_util import *
from src.utils.sensitive_util import *
from src.utils.sizeof_util import get_size

# 敏感词分词过滤器
segment_filter = None
# 敏感词暴力匹配过滤器
basic_filter = None
# 敏感词同音字过滤器
similar_filter = None
# 敏感词简繁体过滤器
traditional_filter = None
# 敏感词排列重组过滤器
permutation_filter = None
# 敏感词干扰字符过滤
noise_filter = None


# 敏感词服务初始化，加载敏感词
def sensitive_init(keywords: List[str], keywords_single: List[str]):

    log.info("分词过滤器")
    global segment_filter
    segment_filter = SegmentFilter()
    segment_filter.add(keywords+keywords_single)

    log.info("暴力过滤器")
    global basic_filter
    basic_filter = BasicFilter()
    basic_filter.add(keywords+keywords_single)

    log.info("同音词过滤器")
    global similar_filter
    similar_filter = SimilarWordFilter()
    similar_filter.add(keywords)

    log.info("简繁体过滤器")
    global traditional_filter
    traditional_filter = TraditionalFilter()
    traditional_filter.add(keywords)

    log.info("重组排列过滤器")
    global permutation_filter
    permutation_filter = PermutationFilter()
    # permutation_filter.add(keywords)
    permutation_filter.add([])

    log.info("干扰符过滤器")
    global noise_filter
    noise_filter = NoiseFilter()
    noise_filter.add(keywords)


def load_sensitive():
    log.info('敏感词服务初始化，加载敏感词')
    sensitive_words = []
    single_sensitive_words = []

    # 遍历文件夹中的所有文件
    for filename in os.listdir(sensitive_path):
        file_path = os.path.join(sensitive_path, filename)

        # 确保是文件而不是文件夹
        if os.path.isfile(file_path):
            with open(file_path, 'r', encoding='utf-8') as file:
                # 读取文件内容并添加到敏感词列表
                for line in file.readlines():
                    words = line.strip()

                    if len(words) == 1:
                        single_sensitive_words.append(words)
                    else:
                        sensitive_words.append(words)

    sensitive_words = list(set(sensitive_words))
    log.info(f"停用词数：{len(sensitive_words)}")
    log.info(f"单字停用词数：{len(single_sensitive_words)}")
    # log.info(f"单字停用词：{single_sensitive_words}")
    log.info(f"列表占用的总内存大小: {get_size(sensitive_words)} 字节")

    sensitive_init(sensitive_words, single_sensitive_words)
    log.info("初始化结束")
# load_sensitive()

# 敏感词过滤
def sensitive_filter(content, strategy=1, rpl="*"):
    """
    :param content: 待处理的文本
    :param strategy: 过滤策略，1：宽松 2：适中 3：严格
    :param rpl: 替换字符
    :return: 处理后的文本
    """
    result = content
    if strategy == 1:
        result = segment_filter.filter(result, rpl)
    elif strategy == 2:
        result = basic_filter.filter(result, rpl)
        result = similar_filter.filter(result, rpl)
    elif strategy == 3:
        result = basic_filter.filter(result, rpl)
        result = similar_filter.filter(result, rpl)
        result = noise_filter.filter(result, rpl)
        result = permutation_filter.filter(result, rpl)
        result = traditional_filter.filter(result, rpl)

    return result

def sensitive_scan(content, strategy=1):
    """
    :param content: 待处理的文本
    :param strategy: 过滤策略，1：宽松 2：适中 3：严格
    :return: 敏感词集合
    """
    result = set()
    if strategy == 1:
        words = segment_filter.scan(content)
        result.update(words)
    elif strategy == 2:
        words = basic_filter.scan(content)
        result.update(words)
        words = similar_filter.scan(content)
        result.update(words)
    elif strategy == 3:
        words = basic_filter.scan(content)
        result.update(words)
        words = similar_filter.scan(content)
        result.update(words)
        words = noise_filter.scan(content)
        result.update(words)
        words = permutation_filter.scan(content)
        result.update(words)
        words = traditional_filter.scan(content)
        result.update(words)

    return result
