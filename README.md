# RAG基础开发版本

* 非特殊定制需求请直接使用此版本

## 1、简介
* 基于es8.9、milvus2.4.1开发
* 支持通过知识库文档、api调用方式获取额外知识

## 2、模块介绍 

### 2.1 知识库模块
**知识库操作**
* 知识库增加
* 知识库名称修改
* 知识库删除
* 知识库列表展示

**文档操作**
* 文档上传
* 文档删除
* 文档列表展示
* 文档召回


**api调用操作**
* api添加
* api修改
* api列表
* api调用


**其他**
* 恢复出厂设置
* 自测前端（定版提交时可根据安全扫描结果自行删除）

## 3、相关文档

* 数据库表结构：https://wiki.qianxin-inc.cn/pages/viewpage.action?pageId=922350216
* api文档地址：https://api.qianxin-inc.cn/independent/home/<USER>/inside/HMCqn4P4980052cff56218c366dc3c77a416c716158c3a3/api/195964/list?spaceKey=sLvGyFa5c9267db12fa34ff2b0cc4a53f6125284364630d

## 4、打包注意

需要创建health保证es和milvus启动后再启动python
es需要创建token
```
_security/api_key
{
  "name": "healthcheck-token",
  "role_descriptors": {
    "healthcheck_role": {
      "cluster": [
        "monitor"
      ]
    }
  }
}
探活命令
["CMD", "curl","-H", "Authorization: ApiKey dHhYV2xKTUJGRUNfTWxCd0tXNjM6b2N2N0s2Wm9RM09KbGRPalhvMUdTdw==",  "-f", "http://localhost:9200/_cluster/health"]

```



## 4、更新时间
20241206