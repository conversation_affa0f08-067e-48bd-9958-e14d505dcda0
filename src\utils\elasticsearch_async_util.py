# -*- coding: utf-8 -*-
"""
Create Time: 2025/5/26 14:04
Author: liuyigong
Description: 异步 Elasticsearch 工具类
"""
import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from src.config.constant import *
import time

from elasticsearch import AsyncElasticsearch
from src.config.constant import es_ip, es_port, es_username, es_password

class AsyncElasticSearchEngine:
    def __init__(self):
        """初始化异步ES客户端"""
        self.host = es_ip
        self.port = es_port
        self.user = es_username
        self.pwd = es_password
        self.client = None
        self._initialized = False
        log.info(f"[sc]初始化异步ES客户端: {self.host}:{self.port} user:{self.user}")
    
    async def get_client(self) -> AsyncElasticsearch:
        """获取或创建ES客户端"""
        if self.client is None:
            if self.user and self.pwd:
                self.client = AsyncElasticsearch(
                    f"{self.host}:{self.port}",
                    basic_auth=(self.user, self.pwd),
                    verify_certs=False
                )
            else:
                self.client = AsyncElasticsearch(f"{self.host}:{self.port}")
            
            if await self.client.ping():
                log.info("[sc]异步ES连接成功")
            else:
                log.error("[sc]异步ES连接失败")
        
        return self.client
    
    async def reconnect(self) -> None:
        """重新连接ES"""
        if self.client:
            await self.client.close()
            self.client = None
        
        self.host = es_ip
        self.port = es_port
        self.user = es_username
        self.pwd = es_password
        log.info(f"[sc]重新连接ES: {self.host}:{self.port} user:{self.user}")
        
        await self.get_client()
    
    async def close(self) -> None:
        """关闭ES连接"""
        if self.client:
            await self.client.close()
            self.client = None
            log.info("[sc]ES连接已关闭")
    
    async def create_sc(self) -> None:
        """创建带同义词的索引（版本问题，暂不支持）"""
        try:
            client = await self.get_client()
            settings = {
                "settings": {
                    "index": {
                        "analysis": {
                            "analyzer": {
                                "synonym_analyzer": {
                                    "tokenizer": "standard",
                                    "filter": ["synonym_filter"]
                                }
                            }
                        },
                        "filter": {
                            "synonym_filter": {
                                "type": "synonym",
                                "synonyms": ["沈杨,沈老师,shenyang"]
                            }
                        }
                    },
                }
            }
            mappings = {
                "mappings": {
                    "properties": {
                        "name": {
                            "type": "text",
                            "analyzer": "synonym_analyzer"
                        }
                    }
                }
            }
            
            await client.indices.create(index="test_sc", settings=settings, mappings=mappings)
            log.info("[sc]test_sc索引创建成功")
        except Exception as e:
            log.error(f"[sc]test_sc索引创建失败: {str(e)}")
            log.error(traceback.format_exc())
    
    async def create(self, index_name: str = "firewall_sc", mapping: Optional[Dict] = None) -> None:
        """创建索引和字段"""
        try:
            client = await self.get_client()
            
            if await client.indices.exists(index=index_name):
                log.info(f"[sc]{index_name}索引已存在")
                return
            
            if mapping is None:
                mapping = {
                    "mappings": {
                        "properties": {
                            "doc_name": {  # 文档名
                                "type": "text",
                                "analyzer": "ik_max_word"
                            },
                            "doc_name_keyword": {  # 文档名判重字段
                                "type": "keyword"
                            },
                            "doc_md5": {  # 文档判重机制
                                "type": "keyword"
                            },
                            "doc_id": {
                                "type": "keyword"
                            },
                            "content_id": {  # 段落ID
                                "type": "keyword"
                            },
                            "title": {  # 级联标题
                                "type": "text",
                                "analyzer": "ik_max_word"
                            },
                            "content": {  # 段落内容
                                "type": "text",
                                "analyzer": "ik_max_word"
                            },
                            "summary": {  # 段落摘要
                                "type": "text",
                                "analyzer": "ik_max_word"
                            },
                            "model": {  # 分段方式
                                "type": "keyword"
                            },
                            "kbase_id": {  # 关联知识库ID
                                "type": "keyword"
                            },
                            "create_time": {  # 创建时间
                                "type": "date",
                                "format": "yyyy-MM-dd HH:mm:ss"
                            }
                        }
                    }
                }
            
            # 创建索引
            await client.indices.create(index=index_name, body=mapping)
            log.info(f"[sc]{index_name}索引创建成功")
        except Exception as e:
            log.error(f"[sc]{index_name}索引创建失败: {str(e)}")
            log.error(traceback.format_exc())
    
    async def search(self, query: Dict, index_name: str = "firewall_sc") -> Dict:
        """执行搜索查询"""
        client = await self.get_client()
        
        try:
            return await client.search(index=index_name, body=query)
        except Exception as e:
            log.error(f"[sc]ES搜索失败: {str(e)}")
            # 只有在出现异常时才尝试重连
            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                log.info("[sc]ES连接异常，尝试重连")
                await self.reconnect()
                client = await self.get_client()
                return await client.search(index=index_name, body=query)
            raise  # 重新抛出其他类型的异常
    
    async def search_async(self, text: str, top_k: int = 5, index_name: str = "firewall_sc",
                             kbase_ids: Optional[List[str]] = None, document_ids: Optional[List[str]] = None, 
                             content_ids: Optional[str] = None) -> Dict:
        """执行防火墙搜索查询"""
        if len(text) == 0 and not content_ids:
            log.info("[sc]搜索内容为空")
            return {"hits": {"hits": []}}
        try:
            client = await self.get_client()
            
            # 构建过滤条件
            filter = []
            if content_ids:
                query = {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "match": {
                                        "content_id": content_ids
                                    }
                                }
                            ]
                        }
                    },
                    "size": top_k
                }
            else:
                if document_ids and kbase_ids:
                    filter.append({
                        "terms": {
                            "doc_id": document_ids
                        }
                    })
                    filter.append({
                        "terms": {
                            "kbase_id": kbase_ids
                        }
                    })
                elif document_ids:
                    filter.append({
                        "terms": {
                            "doc_id": document_ids
                        }
                    })
                elif kbase_ids:
                    filter.append({
                        "terms": {
                            "kbase_id": kbase_ids
                        }
                    })
                query = {
                    "query": {
                        "bool": {
                            "must": [{
                                "multi_match": {
                                    "query": text,
                                    "fields": ["content", "summary", "title", "model"]
                                }

                            }],
                            "should": [
                                {
                                    "match": {
                                        "title": text
                                    }
                                }
                            ],
                            "filter": filter
                        }
                    },
                    "size": top_k
                }
            
            log.info(f"[sc]ES搜索查询: {json.dumps(query, ensure_ascii=False, indent=4)}")
        
            return await client.search(index=index_name, body=query)
        except Exception as e:
            log.error(f"[sc]ES搜索失败: {str(e)}")
            # 只有在出现异常时才尝试重连
            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                log.info("[sc]ES连接异常，尝试重连")
                await self.reconnect()
                client = await self.get_client()
                return await client.search(index=index_name, body=query)
            raise  # 重新抛出其他类型的异常
    
    async def insert(self, document: Dict, index_name: str = "firewall_sc") -> Dict:
        """插入文档"""
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return None
        
        log.info(f"[sc]索引{index_name}入库: {document}")
        return await client.index(index=index_name, body=document)
    
    async def add_synonyms(self, synonyms: List[str], index_name: str = "firewall_sc") -> Dict:
        """添加同义词"""
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return None
        
        log.info(f"[sc]同义词添加: {synonyms}")
        body = {
            "synonyms": {
                "type": "synonym",
                "synonyms": synonyms
            }
        }
        
        return await client.indices.put_settings(index=index_name, body={"analysis": {"filter": body}})
    
    async def bulk(self, actions: List[Dict], index_name: str = "firewall_sc") -> Dict:
        """批量操作"""
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return None
        
        return await client.bulk(body=actions, index=index_name)
    
    async def find_field(self, field: str, field_value: str, index_name: str) -> bool:
        """
        查找字段是否存在
        
        参数:
            field: 字段名
            field_value: 字段值
            index_name: 索引名称
        
        返回:
            是否存在
        """
        log.info(f"[sc]ES异步查找字段: {field}={field_value}")
        
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return False
        
        try:
            # 构建查询
            query = {
                "query": {
                    "term": {
                        field: field_value
                    }
                },
                "size": 1
            }
            
            # 执行查询
            result = await client.search(index=index_name, body=query)
            hits = result.get("hits", {}).get("hits", [])
            
            return len(hits) > 0
        except Exception as e:
            log.error(f"[sc]ES查找字段失败: {str(e)}")
            return False
    
    async def update(self, document_id: str, document: Dict, index_name: str = "firewall_sc") -> Dict:
        """更新文档"""
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return None
        
        return await client.update(index=index_name, id=document_id, body=document)
    
    async def delete(self, ids: Union[str, List[str]], index_name: str) -> bool:
        """
        删除文档
        
        参数:
            ids: 文档ID或ID列表
            index_name: 索引名称
        
        返回:
            是否成功
        """
        log.info(f"[sc]ES异步删除文档: {ids}")
        
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return False
        
        try:
            if isinstance(ids, list):
                # 批量删除
                body = []
                for doc_id in ids:
                    body.append({"delete": {"_index": index_name, "_id": doc_id}})
                
                if body:
                    result = await client.bulk(body=body)
                    log.info(f"[sc]ES批量删除结果: {result}")
            else:
                # 单个删除
                result = await client.delete(index=index_name, id=ids)
                log.info(f"[sc]ES删除结果: {result}")
            
            return True
        except Exception as e:
            log.error(f"[sc]ES删除失败: {str(e)}")
            return False
    
    async def delete_by_field_batch(self, field: str, field_value: str, index_name: str) -> bool:
        """
        根据字段批量删除数据
        
        参数:
            field: 字段名
            field_value: 字段值
            index_name: 索引名称
        
        返回:
            是否成功
        """
        log.info(f"[sc]ES异步批量删除: {field}={field_value}")
        
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return False
        
        try:
            # 构建删除查询
            query = {
                "query": {
                    "term": {
                        field: field_value
                    }
                }
            }
            
            # 执行删除
            result = await client.delete_by_query(index=index_name, body=query)
            log.info(f"[sc]ES批量删除结果: {result}")
            
            return True
        except Exception as e:
            log.error(f"[sc]ES批量删除失败: {str(e)}")
            return False
    
    async def delete_by_query(self, query: Dict, index_name: str = "firewall_sc") -> Dict:
        """根据查询条件删除文档"""
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return None
        
        return await client.delete_by_query(index=index_name, body=query)
    
    async def delete_by_kbase_id(self, kbase_id: str, index_name: str) -> bool:
        """
        根据知识库ID删除数据
        
        参数:
            kbase_id: 知识库ID
            index_name: 索引名称
        
        返回:
            是否成功
        """
        return await self.delete_by_field_batch("kbase_id", kbase_id, index_name)
    
    async def count(self, index_name: str = "firewall_sc") -> Dict:
        """统计文档数量"""
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return None
        
        return await client.count(index=index_name)
    
    async def is_exist(self, index_name: str) -> bool:
        """判断索引是否存在"""
        client = await self.get_client()
        return await client.indices.exists(index=index_name)
    
    async def reindex(self, source_name: str, dest_name: str) -> None:
        """重建索引"""
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return None
        
        body = {
            "source": {
                "index": source_name
            },
            "dest": {
                "index": dest_name
            }
        }
        
        await client.reindex(body=body)
        log.info(f"[sc]数据迁移{source_name}->{dest_name}")
    
    async def drop(self, index_name: str = "firewall_sc") -> None:
        """删除索引"""
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return None
        
        if await client.indices.exists(index=index_name):
            await client.indices.delete(index=index_name)
            log.info(f"[sc]索引{index_name}删除成功")
        else:
            log.info(f"[sc]索引{index_name}不存在")
    
    async def data_refresh(self, index_name: str) -> bool:
        """
        刷新索引数据
        
        参数:
            index_name: 索引名称
        
        返回:
            是否成功
        """
        log.info(f"[sc]ES异步刷新索引: {index_name}")
        
        client = await self.get_client()
        
        if not await client.ping():
            log.info("[sc]ES连接断开")
            await self.reconnect()
            if not await client.ping():
                log.info("[sc]ES重新连接失败")
                return False
        
        try:
            # 执行刷新
            result = await client.indices.refresh(index=index_name)
            log.info(f"[sc]ES刷新索引结果: {result}")
            
            return True
        except Exception as e:
            log.error(f"[sc]ES刷新索引失败: {str(e)}")
            return False

# 创建单例实例
asyncElasticSearchEngine = AsyncElasticSearchEngine()


def get_now_time() -> str:
    """获取当前时间字符串"""
    now = datetime.now()
    return now.strftime("%Y-%m-%d %H:%M:%S")


async def test_async_es():
    """测试异步ES功能"""
    # 创建索引
    await asyncElasticSearchEngine.create("test_async")
    
    # 插入文档
    doc = {
        "doc_name": "测试文档",
        "doc_id": "test001",
        "content_id": "c001",
        "title": "测试标题",
        "content": "这是一个测试文档内容",
        "summary": "测试摘要",
        "kbase_id": "kb001",
        "create_time": get_now_time()
    }
    
    result = await asyncElasticSearchEngine.insert(doc, "test_async")
    print(f"插入结果: {result}")
    
    # 搜索文档
    query = {
        "query": {
            "match": {
                "content": "测试"
            }
        }
    }
    
    search_result = await asyncElasticSearchEngine.search(query, "test_async")
    print(f"搜索结果: {search_result}")
    
    # 删除文档
    await asyncElasticSearchEngine.drop("test_async")
    print("测试完成")
    
    # 关闭连接
    await asyncElasticSearchEngine.close()


if __name__ == "__main__":
    asyncio.run(test_async_es())



