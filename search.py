import datetime

from quart import Quart, request, render_template

import src.service.kbase.file_service_async as fileService
import src.service.kbase.kbase_service_async as kbaseService
import src.service.scene.config_service as configService
from src.config.constant import *
from src.engine.ask2an_bachang_async import askMe as askMeBC, askMeNoLLM
from src.service.sensitive.sensitive_service import sensitive_scan
import warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated")

app = Quart(__name__)

app.config['JSON_AS_ASCII'] = False  # 页面直接显示中文
app.config['DEBUG'] = False
# app.config['MAX_CONTENT_LENGTH'] = 15 * 1024 * 1024 # 限制文件上传的大小


@app.route("/", methods=["GET"])
async def getIndexHtml():
    # 返回主页面
    return await render_template("index.html")


@app.route("/status")
async def status():
    log.info("status ok")
    return "ok"

'''
 ============================================
 RAG部分
'''

@app.route("/kbase/create", methods=["POST"])
async def kbaseCreate():
    newTime = datetime.datetime.now()
    if request.method != 'POST':
        upload = {"status": 202, "message": "错误", "data": {}}
        return json.dumps(upload, ensure_ascii=False)
    # 知识库名称
    form = await request.form
    kbaseName = form.get("kbase_name", "")
    kbaseDesc = form.get("kbase_desc", "")
    kbase_create_res = await kbaseService.createKbase(kbaseName, kbaseDesc)
    log.info(f"kbase create cost: {datetime.datetime.now() - newTime}s")
    return kbase_create_res


@app.route("/kbase/update", methods=["POST"])
async def kbaseUpdate():
    newTime = datetime.datetime.now()
    if request.method != 'POST':
        upload = {"status": 202, "message": "错误", "data": {}}
        return json.dumps(upload, ensure_ascii=False)
    # 知识库id
    form = await request.form
    kbaseId = form.get("kbase_id", "")
    kbaseName = form.get("kbase_name", "")
    kbaseDesc = form.get("kbase_desc", "")
    kbase_update_res = await kbaseService.updateKbase(kbaseId, kbaseName, kbaseDesc)
    log.info(f"kbase update cost: {datetime.datetime.now() - newTime}s")
    return kbase_update_res


@app.route("/kbase/delete", methods=["POST"])
async def kbaseDelete():
    newTime = datetime.datetime.now()
    if request.method != 'POST':
        upload = {"status": 202, "message": "错误", "data": {}}
        return json.dumps(upload, ensure_ascii=False)
    # 知识库id
    form = await request.form
    kbaseId = form.get("kbase_id", "")
    del_res = await kbaseService.deleteKbase(kbaseId)
    log.info(f"kbase delete cost: {datetime.datetime.now() - newTime}s")
    return del_res


@app.route("/kbase/list", methods=["GET", "POST"])
async def kbaseList():
    newTime = datetime.datetime.now()
    list_kbase_ = await kbaseService.listKbase()
    log.info(f"kbase list cost: {datetime.datetime.now() - newTime}s")
    return list_kbase_


@app.route("/file/upload", methods=["POST"])
async def fileUpload():
    '''
        知识库文档上传
    :return:
    '''
    newTime = datetime.datetime.now()
    if request.method != 'POST':
        upload = {"status": 202, "message": "methods错误", "data": {}}
        return json.dumps(upload, ensure_ascii=False)
    # 需要解析的文件
    uploadFile = (await request.files).get("file")
    form = await request.form
    # 上传到的知识库id
    kbaseId = form.get("kbase_id", 0)
    # 分段形式（章节或长度）
    model = form.get("model", "")
    # 分段的大小
    maxTokens = form.get("max_tokens", "")
    # 上下文保留的字符数
    chunkOverlap = form.get("chunk_overlap", "")
    # 章节分割最小保留等级
    lastLevel = form.get("last_level", "")
    # json解析对应关系
    mapping = form.get("mapping", {})
    # json解析content是否合并
    merge_content = form.get("merge_content", True)

    parameters = {
        "kbaseId": kbaseId,
        "model": model,
        "maxTokens": maxTokens,
        "chunkOverlap": chunkOverlap,
        "lastLevel": lastLevel,
        "mapping": mapping,
        "merge_content": merge_content
    }

    upload_res = await fileService.doUpload(uploadFile, parameters)
    log.info(f"upload cost: {datetime.datetime.now() - newTime}s")
    return upload_res


@app.route("/file/list", methods=["POST"])
async def fileShow():
    '''
        知识库文档列表查看
    :return:
    '''
    newTime = datetime.datetime.now()
    if request.method != 'POST':
        listInfo = {"status": 202, "message": "错误", "data": {}}
        return json.dumps(listInfo, ensure_ascii=False)
    # 查询的知识库id
    if request.method == 'GET':
        form = request.args
    else:
        form = await request.form
    kbaseId = form.get("kbase_id", 0)
    list_res = await fileService.doList(kbaseId)
    log.info(f"file list cost: {datetime.datetime.now() - newTime}s")
    return list_res


@app.route("/file/delete", methods=["POST"])
async def fileDelete():
    '''
        知识库文档删除
    :return:
    '''
    newTime = datetime.datetime.now()
    if request.method != 'POST':
        listInfo = {"status": 202, "message": "错误", "data": {}}
        return json.dumps(listInfo, ensure_ascii=False)
    # 查询的知识库id
    if request.method == 'GET':
        form = request.args
    else:
        form = await request.form
    docId = form.get("doc_id", 0)
    res = await fileService.doDelete(docId)
    log.info(f"file delete cost: {datetime.datetime.now() - newTime}s")
    return res


@app.route("/ask", methods=["POST", "GET"])
async def ask():
    '''
        RAG场景问答对外接口
    :return:
    '''
    newTime = datetime.datetime.now()
    if request.method == 'GET':
        form = request.args
    else:
        form = await request.form

    query = form.get("query", "")
    context = form.get("context", None)
    session_id = form.get("session_id", None)

    if not query or not session_id:
        raise ValueError("Missing required parameters in the request.")
    data = {}
    try:
        if query and len(query) > 0:
            data['query'] = query
            data['context'] = context
            data['session_id'] = session_id
            data['kb_list'] = ""
            data['doc_list'] = ""
            result = await askMeBC(query, data)

            log.info(f"answer cost: {datetime.datetime.now() - newTime}s")
            return json.dumps(result, ensure_ascii=False)
            pass
        else:
            return json.dumps({'code': 202, "msg": "parameters wrong", "data": {}}, ensure_ascii=False)
    except Exception as e:
        log.error(Log().trace_back_info())
        return json.dumps({'code': 500, "msg": str(Log().trace_back_info()), "data": {}}, ensure_ascii=False)


@app.route("/asknollm", methods=["POST", "GET"])
async def askNoLLM():
    '''
        RAG场景问答对外接口
    :return:
    '''
    newTime = datetime.datetime.now()
    if request.method == 'GET':
        form = request.args
    else:
        form = await request.form

    query = form.get("query", "")
    context = form.get("context", None)
    session_id = form.get("session_id", None)
    kb_list = form.get("kb_list", None)
    doc_list = form.get("doc_list", None)

    if not query or not context or not session_id:
        raise ValueError("Missing required parameters in the request.")
    data = {}
    try:
        if query and len(query) > 0 and kb_list:
            data['query'] = query
            data['context'] = context
            data['session_id'] = session_id
            data['kb_list'] = kb_list
            data['doc_list'] = doc_list
            result = await askMeNoLLM(query, data)

            log.info(f"answer cost: {datetime.datetime.now() - newTime}s")
            return json.dumps(result, ensure_ascii=False)
            pass
        else:
            return json.dumps({'code': 202, "msg": "parameters wrong", "data": {}}, ensure_ascii=False)
    except Exception as e:
        log.error(Log().trace_back_info())
        return "wrong"


@app.route("/scene/list", methods=["GET"])
async def sceneList():
    '''
        RAG场景问答列表
    :return:
    '''
    return json.dumps(await configService.sceneAll(), ensure_ascii=False)
'''
 ============================================
'''


@app.route("/system/restore", methods=["POST"])
async def restoreFactorySettings():
    newTime = datetime.datetime.now()
    if request.method != 'POST':
        listInfo = {"status": 202, "message": "错误", "data": {}}
        return json.dumps(listInfo, ensure_ascii=False)
    form = await request.form
    secret = form.get("secret_Key", "")
    if not secret:
        listInfo = {"status": 202, "message": "参数错误", "data": {}}
        return json.dumps(listInfo, ensure_ascii=False)
    restore_res = await kbaseService.restoreFactorySettings(secret)
    log.info(f"restore cost: {datetime.datetime.now() - newTime}s")
    return restore_res



'''
敏感词扫描
'''
@app.route("/sensitive/scan", methods=["POST"])
async def content_scan():
    if request.method != 'POST':
        resultInfo = {"status": 202, "message": "错误请求", "data": []}
        return json.dumps(resultInfo, ensure_ascii=False)
    form = await request.form
    content = form.get("content", "")
    type = form.get("type", "1")
    if not content:
        resultInfo = {"status": 201, "message": "数据为空", "data": []}
        return json.dumps(resultInfo, ensure_ascii=False)
    if type in ["1", "2", "3"]:
        data = sensitive_scan(content, int(type))
        if data:
            resultInfo = {"status": 200, "message": "成功", "data": list(data)}
            return json.dumps(resultInfo, ensure_ascii=False)
        else:
            resultInfo = {"status": 210, "message": "无敏感词", "data": []}
            return json.dumps(resultInfo, ensure_ascii=False)
    else:
        resultInfo = {"status": 202, "message": "类型错误", "data": []}
        return json.dumps(resultInfo, ensure_ascii=False)


# 在应用启动前初始化
@app.before_serving
async def startup():
    """
    应用启动前执行的初始化函数
    """
    from src.service.kbase.kbase_service_async import initialize_app
    await initialize_app()

# 主函数，使用asyncio.run运行应用
if __name__ == "__main__":
    app.run(host="0.0.0.0", port=9252)
