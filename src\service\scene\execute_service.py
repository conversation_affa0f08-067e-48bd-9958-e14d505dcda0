import json
import time
import requests
import traceback
import urllib.parse
from jinja2 import Template
from src.config.constant import log

def doExecute(sceneConfig, parameters):
    '''
        执行联网数据获取模块
    :param sceneConfig: 配置信息
    :param parameters: 输入的参数
    :return:
    '''
    dataStr = ""
    log.info(f"【场景数据获取执行】:{sceneConfig}")
    try:
        currentType = sceneConfig.get("type", "")
        if currentType == "http-request":
            dataStr = doRequest(sceneConfig, parameters)
        else:
            dataStr = ""
        # 去除最后的换行
        dataStr = dataStr.strip()
    except Exception:
        log.error(f"【场景数据查询模块执行失败】:{sceneConfig}")
        log.error(f"【场景数据查询模块执行失败】:{parameters}")
        log.error(traceback.format_exc())
    return dataStr

def doRequest(config, parameters):
    '''
        执行http请求
    :param config: 配置信息
    :param parameters: 输入的参数
    :return:
    '''

    parameters = replaceConfigParams(config.get("config_params", {}), parameters)

    parametersList = getParametersList(parameters)
    url = config.get("http_url", "")
    method = config["http_method"].upper()
    headersStr = config.get("http_headers", "")
    paramsStr = config.get("http_params", "")
    bodyData = config.get("http_body", {}).get("data", "")
    bodyType = config.get("http_body", {}).get("type", "")
    log.info(f"【api数据查询模块替换的参数】:{parametersList}")
    # 替换所有入参
    for paramOne in parametersList:
        strValue = paramOne["value"][1:-1] if (paramOne["value"].startswith('"')
                                               and paramOne["value"].endswith('"')) else paramOne["value"]
        headersStr = headersStr.replace(paramOne["name"], strValue)
        paramsStr = paramsStr.replace(paramOne["name"], strValue)
        if bodyType == "json":
            bodyData = bodyData.replace(paramOne["name"], paramOne["value"])
        else:
            bodyData = bodyData.replace(paramOne["name"], strValue)
    log.info(f"【api数据查询模块替换后的headers】:{headersStr}")
    log.info(f"【api数据查询模块替换后的params】:{paramsStr}")
    log.info(f"【api数据查询模块替换后的body】:{bodyData}")

    headers = paramsStrToDic(headersStr)
    params = paramsStrToDic(paramsStr)

    # 获取body参数
    if bodyType == "json":
        headers["Content-Type"] = "application/json"
        body = json.dumps(json.loads(bodyData))
    elif bodyType == "form-data":
        body = paramsStrToDic(bodyData)
    else:
        body = {}

    # 获取最终url
    if url.endswith("&"):
        url = url[:-1]
    if "?" in url:
        if url.endswith("?"):
            url = url + urllib.parse.urlencode(params)
        else:
            url = url + "&" + urllib.parse.urlencode(params)
    else:
        url = url + "?" + urllib.parse.urlencode(params)

    log.info(f"【api数据查询模块替换后的url】:{url}")
    response = requests.request(method, url, headers=headers, data=body, verify=False, timeout=10)
    result = response.text
    log.info(f"【api数据查询模块返回结果】:{result}")
    result = json.loads(result)
    responseParser = str(config.get("response_parser", ""))
    if responseParser.strip() != "":
        result = Template(responseParser).render(response=result).strip()
        log.info(f"【api数据查询模块解析后结果】:{result}")
    return result

def replaceConfigParams(configParams, parameters):
    '''
        替换配置中的参数到请求参数中
    :param configParams: 配置参数
    :param parameters: 请求参数
    :return:
    '''
    for paramName in configParams.keys():
        parameters[paramName] = eval(configParams[paramName])
    return parameters


def getParametersList(parameters):
    '''
        获取解析后的参数名和参数值
    :param param: 需要解析的参数
    :return:
    '''
    parametersList = []
    for name in parameters.keys():
        realName = "{{#" + name + "#}}"
        realValue = json.dumps(parameters[name], ensure_ascii=False)
        parametersList.append({
            "name": realName,
            "value": realValue
        })
    return parametersList

def paramsStrToDic(paramsStr):
    '''
        参数str转换位dict
    :param paramsStr: 参数字符串
    :return: 转换后的字段
    '''
    paramsDic = {}
    # 空字符直接返回空字典
    if paramsStr.strip() == "":
        return {}
    paramsList = paramsStr.split("\n")
    for one in paramsList:
        if ":" not in one: continue
        name = one[:one.index(":")]
        value = one[one.index(":") + 1:]
        if name and value:
            paramsDic[name] = value
    return paramsDic