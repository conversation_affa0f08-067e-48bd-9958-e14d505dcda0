# -*- coding: utf-8 -*-



jsstr = "{\n   \"kbase_ids\": [\"7218891955\", \"4259195940\"],\n   \"queries\": {\n     \"7218891955\": \"CNNVD-202503-3145的漏洞详情\",\n     \"4259195940\": \"访问控制错误机制的作用和含义\"\n   }\n}"

raw_jsstr= {'query': {'bool': {'must': [{'multi_match': {'query': 'CNNVD-2025-3145的漏洞详情以及访问控制错误的含义', 'fields': ['content', 'summary', 'title', 'model']}}], 'should': [{'match': {'title': 'CNNVD-2025-3145的漏洞详情以及访问控制错误的含义'}}], 'filter': [{'terms': {'kbase_id': [2205124352]}}]}}, 'size': 10}

import json

print(json.dumps(raw_jsstr, ensure_ascii=False))

# loads = json.loads(jsstr)
# print(loads)

prom = '''
角色：你是个聪明的助手。你的名字是Q-RAG。
    任务：从知识库中总结信息，回答用户的问题。
    要求和限制：
    -尽可能用知识库中的与问题相关的信息和原文回答。
    -不要编造，尤其是数字。
    -如果来自知识的信息与用户的问题无关，只需说：对不起，没有提供相关信息。
    -回答markdown格式的文本。
    -回答用户的问题的语言。
    -不要编造，尤其是数字。
    
    来自知识库的信息
    
【来源1】
文档来源: {"SHA-1": "SHA-1（英语：Secure Hash Algorithm 1，中文名：安全散列算法1）是一种密码散列函数，美国国家安全局设计，并由美国国家标准技术研究所（NIST）发布为联邦数据处理标准（FIPS）。SHA-1可以生成一个被称为消息摘要的160位（20字节）散列值，散列值通常的呈现形式为40个十六进制数。SHA-1\nSecure Hash Algorithm 1\n1995年\n美国国家安全局\n密码散列函数\n密码学\nSHA-1已经不再视为可抵御有充足资金、充足计算资源的攻击者。2005年，密码分析人员发现了对SHA-1的有效攻击方法，这表明该算法可能不够安全，不能继续使用，自2010年以来，许多组织建议用SHA-2才盼元屑店钻辨请妹或棕您SHA跨喇端-3来替换SH驼删懂再匙A-1。Microsoft、Google以及Mozilla都宣布，它们旗下的浏览器将在2017年前停止接受使用SHA-1算法签炒捆堡名的SSL证书。\n2017年2月23日，CWI Amsterdam与Google宣布了一个成功的SHA-1碰撞攻击，发布了两份内容不同但SHA-1散列值相同的PDF文件作为概念证明。\n最初载明的算法于1993年发布，称做安全散列标准（Secure Hash Standard），FIPSPUB 180。这个版本现在常被称为SHA-0。它在发布之后很快就被NSA撤回，并且由1995年发布的修订版本FIPS PUB 180-1（通常称为SHA-1）取代。SHA-1和SHA-0的算法只在压缩函数的消息转换部分差了一个比特的循环位移。根据NSA的说法，它修正了一个在原始算法中会降低散列安全性的弱点。然而NSA并没有提供任何进一步的解释或证明该弱点已被修正。而后SHA-0和SHA-1的弱点相继被攻破，SHA-1似乎是显得比SHA-0有抵抗性，这多少证实了NSA当初修正算法以增进安全性的声明。\nSHA-0和SHA-1可将一个最大2^64比特的消息，转换成一串160位的消息摘要；其设计原理相似于MIT教授Ronald L. Rivest所设计的密码学散列算法MD4和MD5。在CRYPTO98上，两位法国研究者提出一种对SHA-0的攻击方式：在2的计算复杂度之内，就可以发现一次碰撞（即两个不同的消息对应到相同的消息摘要）；这个数字小于生日攻击法所需的2，也就是说，存在一种算法，使其安全性不到一个理想的散列函数抵抗攻击所应具备的计算复杂度。\n2004年时，Biham和Chen也发现了SHA-0的近似碰撞，也就是两个消息可以散列出几乎相同的数值；其中162比特中有142比特相同。他们也发现了SHA-0的完整碰撞（相对于近似碰撞），将本来需要80次方的复杂度降低到62次方。\n2004年8月12日，Joux, Carribault, Lemuet和Jalby宣布找到SHA-0算法的完整碰撞的方法，这是归纳Chabaud和Joux的攻击所完成的结果。发现一个完整碰撞只需要2的计算复杂度。他们使用的是一台有256颗Itanium2处理器的超级计算机，约耗80,000 CPU工时。\n2004年8月17日，在CRYPTO2004的Rump会议上，王小云，冯登国（Feng）、来学嘉（Lai），和于红波（Yu）宣布了攻击MD5、SHA-0和其他散列函数的初步结果。他们攻击SHA-0的计算复杂度是2，这意味着他们的攻击成果比Joux还有其他人所做的更好。请参见MD5安全性。\n2005年二月，王小云和殷益群、于红波再度发表了对SHA-0破密的算法，可在2的计算复杂度内就找到碰撞。\n鉴于SHA-0的破密成果，专家们建议那些计划利用SHA-1实现密码系统的人们也应重新考虑。在2004年CRYPTO会议结果公布之后，NIST即宣布他们将逐渐减少使用SHA-1，改以SHA-2取而代之。\n2005年，Rijmen和Oswald发表了对SHA-1较弱版本（53次的加密循环而非80次）的攻击：在2的计算复杂度之内找到碰撞。\n2005年二月，王小云、殷益群及于红波发表了对完整版SHA-1的攻击，只需少于2的计算复杂度，就能找到一组碰撞。（利用生日攻击法找到碰撞需要2的计算复杂度。）\n这篇论文的作者们写道；“我们的破密分析是以对付SHA-0的差分攻击、近似碰撞、多区块碰撞技术、以及从MD5算法中查找碰撞的消息更改技术为基础。没有这些强力的分析工具，SHA-1就无法破解。”此外，作者还展示了一次对58次加密循环SHA-1的破密，在2个单位操作内就找到一组碰撞。完整攻击方法的论文发表在2005年八月的CRYPTO会议中。\n殷益群在一次面谈中如此陈述：“大致上来说，我们找到了两个弱点：其一是前置处理不够复杂；其二是前20个循环中的某些数学运算会造成不可预期的安全性问题。”\n2005年8月17日的CRYPTO会议尾声中王小云、姚期智、姚储枫再度发表更有效率的SHA-1攻击法，能在2个计算复杂度内找到碰撞。\n2006年的CRYPTO会议上，Christian Rechberger和Christophe De Cannière宣布他们能在容许攻击者决定部分原消息的条件之下，找到SHA-1的一个碰撞。\n在密码学的学术理论中，任何攻击方式，其计算复杂度若少于暴力搜索法所需要的计算复杂度，就能被视为针对该密码系统的一种破密法；但这并不表示该破密法已经可以进入实际应用的阶段。\n就应用层面的考量而言，一种新的破密法出现，暗示着将来可能会出现更有效率、足以实用的改良版本。虽然这些实用的破密法版本根本还没诞生，但确有必要发展更强的散列算法来取代旧的算法。在“碰撞”攻击法之外，另有一种反译攻击法（Pre-image attack），就是由散列出的字符串反推原本的消息；反译攻击的严重性更在碰撞攻击之上，但也更困难。在许多会应用到密码散列的情境（如用户密码的存放、文件的数字签名等）中，碰撞攻击的影响并不是很大。举例来说，一个攻击者可能不会只想要伪造一份一模一样的文件，而会想改造原来的文件，再附上合法的签名，来愚弄持有公钥的验证者。另一方面，如果可以从密文中反推未加密前的用户密码，攻击者就能利用得到的密码登录其他用户的账户，而这种事在密码系统中是不能被允许的。但若存在反译攻击，只要能得到指定用户密码散列过后的字符串（通常存在影档中，而且可能不会透露原密码信息），就有可能得到该用户的密码。\n2017年2月23日，Google公司公告宣称他们与CWI Amsterdam合作共同创建了两个有着相同的SHA-1值但内容不同的PDF文件，这代表SHA-1算法已被正式攻破。\n以下是SHA-1算法的伪代码：\nNote: All variables are unsigned 32 bits and wrap modulo 232when calculating\nİniyorlar variables:\nh0:= 0x67452301\nh1:= 0xEFCDAB89\nh2:= 0x98BADCFE\nh3:= 0x10325476\nh4:= 0xC3D2E1F0\nPre-processing:\nappend the bit '1' to the message\nappend k bits '0', where k is the minimum number >= 0 such that the resulting message\nlength (in bits) is congruent to 448(mod 512)\nappend length of message (before pre-processing), in bits, as 64-bit big-endian integer\nProcess the message in successive 512-bit chunks:\nbreak message into 512-bit chunks\nfor each chunk\nbreak chunk into sixteen 32-bit big-endian words w[i], 0 ≤ i ≤ 15\nExtend the sixteen 32-bit words into eighty 32-bit words:\nfor i from 16 to 79\nw[i]:= (w[i-3] xor w[i-8] xor w[i-14] xor w[i-16]) leftrotate 1\nInitialize hash value for this chunk:\na:= h0\nb:= h1\nc:= h2\nd:= h3\ne:= h4\nMain loop:\nfor i from 0 to 79\nif 0 ≤ i ≤ 19 then\nf:= (b and c) or ((not b) and d)\nk:= 0x5A827999\nelse if 20 ≤ i ≤ 39\nf:= b xor c xor d\nk:= 0x6ED9EBA1\nelse if 40 ≤ i ≤ 59\nf:= (b and c) or (b and d) or(c and d)\nk:= 0x8F1BBCDC\nelse if 60 ≤ i ≤ 79\nf:= b xor c xor d\nk:= 0xCA62C1D6\ntemp:= (a leftrotate 5) + f + e + k + w[i]\ne:= d\nd:= c\nc:= b leftrotate 30\nb:= a\na:= temp\nAdd this chunk's hash to result so far:\nh0:= h0 + a\nh1:= h1 + b\nh2:= h2 + c\nh3:= h3 + d\nh4:= h4 + e\nProduce the final hash value (big-endian):\ndigest = hash = h0 append h1 append h2 append h3 append h4\n上述关于f表达式列于FIPS PUB 180-1中，以下替代表达式也许也能在主要循环里计算f：\n(0 ≤ i ≤ 19): f:= d xor (b and (c xor d))  (alternative)\n(40 ≤ i ≤ 59): f:= (b and c) or (d and (b or c))\n(alternative 1)(40 ≤ i ≤ 59): f:= (b and c) or (d and (b xor c))\n(alternative 2)(40 ≤ i ≤ 59): f:= (b and c) + (d and (b xor c)) (alternative 3)"}

【来源2】
文档来源: {"Trojan-Dropper.Win32.VB": "一、病毒基本情况：\n病毒名称：Trojan-Dropper.Win32.VB.rj\n病毒别名：无\nSHA1　：b86e419783b2d1ca9a5d4ea7de4711cf3da7a83b\n加壳类型：无\n开发工具：Microsoft Visual Basic 5.0 / 6.0\n二、病毒行为：\n1、病毒运行后会生成以下文件：\n%windir%/svchost.exe　(458752 字节， 回收站图标)\n%windir%/ravfree.exe　(307640 字节，安装程序图标， 灰鸽子木马)\n%ProgramFiles%/Common Files/Microsoft Shared/MSINFO/servieces.exe　(307640 字节，安装程序图\n标， 灰鸽子木马)\n%windir%/system32/_servieces.exe　(307640 字节，安装程序图标， 灰鸽子木马)\n2、修改注册表添加一个启动项：\n键路径：HKEY_LOCAL_MACHINE/SOFTWARE/Microsoft/Windows NT/CurrentVersion/Winlogon\n键名：Shell\n键值：Explorer.exe %windir%/svchost.exe\n3、为 灰鸽子木马添加一个服务：\n服务名称：system starmize\n显示名称：system starmize\n描述：系统自带启动优化\n可执行文件路径：%ProgramFiles%/Common Files/Microsoft Shared/MSINFO/servieces.exe\n启动类型：自动\n4、修改系统时间。通过执行cmd.exe /c date 1980-01-01命令，将系统时间修改为1980年。\n5、监视U盘等移动设备，拷贝自身到U盘里面并命名为recycle.exe，写入autorun.inf，以达到随U盘传播的目的。\n6、病毒释放的灰鸽木马会连接https://sx.yixiti.net.ru/i/i.txt  下载i.txt文件，根据i.txt文件中的内容连接黑客\n并接受其控制。\n7、监视自身文件及启动项，防止被删除。\n三、解决方案：\n1、删除注册表内的启动项。修改注册表删除病毒启动项，删除键值内的%windir%/svchost.exe：\n键路径：HKEY_LOCAL_MACHINE/SOFTWARE/Microsoft/Windows NT/CurrentVersion/Winlogon\n键名：Shell\n键值：Explorer.exe %windir%/svchost.exe\n2、重启计算机，进入安全模式。\n3、删除病毒文件。删除以下文件：\n%windir%/svchost.exe\n%windir%/ravfree.exe\n%ProgramFiles%/Common Files/Microsoft Shared/MSINFO/servieces.exe\n%windir%/system32/_servieces.exe\n4、删除病毒添加的服务。打开 超级巡警，使用服务管理功能删除名为system starmize的服务。\n5、修正系统时间。\n四、对预防此病毒的建议：\n由于此病毒是通过U盘传播的，所以建议使用 超级巡警的U盘免疫对U盘进行免疫，并且废除系统的自动运行功能。在\n将U盘插入到电脑时要对U盘进行杀毒，然后再使用。"}

【来源3】
文档来源: {"SqlServer函数": "1 字符串函数\n1.1 长度与分析用\ndatalength(Char_expr) 返回字符串包含字符数,但不包含后面的空格\nsubstring(expression,start,length) 不多说了,取子串\nright(char_expr,int_expr) 返回字符串右边int_expr个字符\n1.2 字符操作类\nupper(char_expr) 转为大写\nlower(char_expr) 转为小写\nspace(int_expr) 生成int_expr个空格\nreplicate(char_expr,int_expr)复制字符串int_expr次\nreverse(char_expr) 反转字符串\nstuff(char_expr1,start,length,char_expr2) 将字符串char_expr1中的从\nstart开始的length个字符用char_expr2代替\nltrim(char_expr) rtrim(char_expr) 取掉空格\nascii(char) char(ascii) 两函数对应,取ascii码,根据ascii码取字符\n1.3 字符串查找\ncharindex(char_expr,expression) 返回char_expr的起始位置\npatindex(\"%pattern%\",expression) 返回指定模式的起始位置,否则为0\ntrunc(45.923，1) 按指定精度截断十进制数，结果：45.9 ，此为oracle函数。\nmod(1600,300) 求除法余数 ，结果：100。\nabs(numeric_expr) 求绝对值。\nceiling(numeric_expr) 取大于等于指定值的最小整数（即向上取整）。\nfloor(numeric_expr) 小于等于指定值得最大整数（即向下取整）。\navg（numeric_expr）取平均数。\nexp(float_expr) 返回e的n次方。\npi() 3.1415926.........（即圆周率π）。\npower(底数m，指数n) 返回m的n次方。\nrand([int_expr]) 随机数产生器。\nround(numeric_expr,int_expr) 按照int_expr规定的精度四舍五入。\nsign(int_expr) 根据正数,零,负数,返回+1,0,-1。\nsqrt(float_expr) 返回平方根。\ngetdate() 返回日期\ndatename(datepart,date_expr) 返回名称如 June\ndatepart(datepart,date_expr) 取日期一部份\ndatediff(datepart,date_expr1.dateexpr2) 日期差\ndateadd(datepart,number,date_expr) 返回日期加上 number\n上述函数中datepart的\n写法 取值和意义\nyy 1753-9999 年份\nqq 1-4 刻\nmm 1-12 月\ndy 1-366 日\ndd 1-31 日\nwk 1-54 周\ndw 1-7 周几\nhh 0-23 小时\nmi 0-59 分钟\nss 0-59 秒\nms 0-999 毫秒\n日期转换\nconvert()\nsuser_name() 用户登录名\nuser_name() 用户在数据库中的名字\nuser 用户在数据库中的名字\nshow_role() 对当前用户起作用的规则\ndb_name() 数据库名\nobject_name(obj_id)数据库对象名\ncol_name(obj_id,col_id) 列名\ncol_length(objname,colname) 列长度\nvalid_name(char_expr) 是否是有效标识符\nconvert (数据类型[(长度)]，表达式[，样式])\n将一种数据类型的表达式显式转换为另一种数据类型的表达式；\n长度：如果数据类型允许设置长度，可以设置长度，例如 varchar(10);\n样式：用于将日期类型数据转换为字符数据类型的日期格式的样式。\ncast (表达式 AS 数据类型[(长度)])\n将一种数据类型的表达式显式转换为另一种数据类型的表达式。\n例如：select cast(123 as nvarchar) 返回123\nselect N'年龄：' + cast(23 as nvarchar) 返回 年龄：23\nnewid 无参数\n返回一个GUID(全局唯一表示符)值\n例如：select newid()\n返回：2E6861EF-F4DB-4FFE-86EB-637482FE982J2\nisnumeric (任意表达式)\n判断表达式是否为数值类型或者是否可以转换成数值。\n是：返回1，不是：返回0\n例如：select isnumeric(1111) 返回 1\nselect isnumeric('123rr') 返回 0\nselect isnumeric('123') 返回 1\nisnull (任意表达式1，任意表达式2)\n如果任意表达式1不为NULL，则返回它的值；否则，在将任意表达式2的类型转换为任意表达式1的类型(如果这两个类型不同)后，返回任意表达式2的值。\n例如：select isnull(null,N'没有值') 返回 没有值\nselect isnull(N'具体的值'，N'没有值') 返回 具体的值\nisdate (任意表达式)\n确定输入表达式是否为有效日期或可转成有效的日期；\n是：返回1，不是：返回0\n例如：select isdate(getdate()) 返回1\nselect isdate('2013-01-02') 返回1\nselect isdate('198') 返回0\nrow_number 无参数\n为结果集内每一行进行编号，从1开始后面行依次加1，常用于产生序号；\n例如：select row_number() over(order by userid desc) as [NO],username,password from T_USER\ncount()\n返回组中的总条数，count(*)返回组中所有条数，包括NULL值和重复值项，如果抒写表达式，则忽略空值，表达式为任意表达式。\nmax()\n返回组中的最大值，空值将被忽略，表达式为数值表达式，字符串表达式，日期。\nmin()\n返回组中的最小值，空值将被忽略，表达式为数值表达式，字符串表达式，日期。\nsum()\n返回组中所有值的和，空值将被忽略，表达式为数据表达式。\navg()\n返回组中所有值的平均值，空值将被忽略，表达式为数据表达式。"}

    以上是来自知识库的信息。
    
    已下是用户问题:
    介绍一下SHA1编码
    以上是用户问题
'''

data = {
  "model": "deepseek-r1:32b",
  "prompt": "你好",
  "stream": False
}

data["prompt"] = prom

# print(json.dumps(data, ensure_ascii=False, indent=4))

results = [{
    "kb_id": "7218891955",
    "doc": "CNNVD-202503-3145的漏洞详情"
  },
  {
    "kb_id": "4259195940",
    "doc": "访问控制错误机制的作用和含义"
  }
]

for i, result in enumerate(results, 1):
    print(f"\n【来源{i}】\n")
    print(f"文档内容: {result.get('doc', '无内容')}\n")