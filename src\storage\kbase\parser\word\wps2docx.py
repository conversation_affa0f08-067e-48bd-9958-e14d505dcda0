import os
import subprocess
import shutil
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename

app = Flask(__name__)

base_path = '/home/<USER>/apps/nlp/wps2docx'
upload_path = os.path.join(base_path, 'upload')
convert_path = os.path.join(base_path, 'convert')

os.makedirs(upload_path, exist_ok=True)
os.makedirs(convert_path, exist_ok=True)


@app.route('/convert', methods=['POST'])
def convert_file():
    # 检查请求中是否有文件
    if 'file' not in request.files:
        return jsonify({'error': '没有文件部分'}), 400

    uploaded_files = []
    if 'file' in request.files:
        # 获取所有上传的文件
        uploaded_files = request.files.getlist('file')

    print(uploaded_files)
    if not uploaded_files:
        file = request.files['file']
        # 如果用户没有选择文件，浏览器也会提交一个没有文件名的空部分
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        uploaded_files.append(file)



    type = request.form["type"]
    if type not in ("txt", "doc", "docx"):
        return jsonify({'error': '没有选择文件转换格式'}), 400

    result = []

    for file in uploaded_files:
        if file and allowed_file(file.filename):
            secureFilename = secure_filename(file.filename)
            print(secureFilename)
            filename = file.filename
            print(filename)
            file_path = os.path.join(upload_path, filename)
            file_type = filename.rsplit('.', 1)[1].lower()
            print(file_type)
            # 保存文件
            file.save(file_path)

            # 调用LibreOffice命令行工具进行转换
            success = convert_wps_to_format(file_path, convert_path, type)

            if success:
                # 返回转换后的文件路径
                print(jsonify({'message': '转换成功', 'output_path': convert_path + '.{}'.format("type")}))

                result.append({'message': '转换成功', 'output_path': convert_path + '.{}'.format("type")})
            else:
                # 转换失败
                print(jsonify({'error': '转换失败'}))
                result.append({'error': '转换失败'})

        else:
            print(file.filename)
            print(jsonify({'error': '不支持的文件类型'}), 400)

    return result


def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in {'wps', 'odt', 'doc', 'docx'}

def convert_wps_to_format(input_path, output_path, format_type='docx'):
    """
    使用 LibreOffice 命令行工具转换文件格式
    """
    soff_command = "soffice"
    if not shutil.which('soffice'):
        os.environ['soffic'] = "/opt/libreoffice7.6/program/soffice"
        print(os.environ['soffic'])
        soff_command = "/opt/libreoffice7.6/program/soffice"


    command = [
        soff_command,
        '--headless',               # 无头模式
        '--convert-to', format_type, # 指定目标格式
        input_path,                 # 输入文件路径
        '--outdir', output_path      # 输出目录
    ]

    try:
        subprocess.run(command, check=True)
        print(f"转换成功: {output_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"转换失败: {e}")
        return False

if __name__ == '__main__':
    app.run(port=7078, host='0.0.0.0', debug=False)