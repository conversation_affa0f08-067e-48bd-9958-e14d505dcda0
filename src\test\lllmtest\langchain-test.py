from langchain_experimental.text_splitter import SemanticChunker
from src.test.lllmtest.local_embedding import LocalHFEmbeddings
from src.config.constant import log
from src.config.sys_config import BASE_PATH
from transformers import AutoModel, AutoTokenizer

# 加载模型
tokenizer = AutoTokenizer.from_pretrained(f"{BASE_PATH}/src/calculate/embedding_model")
model = AutoModel.from_pretrained(f"{BASE_PATH}/src/calculate/embedding_model")
model.to('cpu')
log.info("【向量化模型】加载成功")


# 初始化自定义Embeddings
local_embeddings = LocalHFEmbeddings(model=model, tokenizer=tokenizer)

# 创建分块器
text_splitter = SemanticChunker(local_embeddings)


text = ["气候变化导致全球气温上升","北极冰川融化速度加快","新能源汽车销量逐年增长","锂电池技术成本持续下降。"]


# 执行语义分块
docs = text_splitter.create_documents(text)

print(len(docs))
for i, doc in enumerate(docs):
    print('-' * 50)
    print(f"分块 {i + 1}: {doc.page_content}\n")
