# -*- coding: utf-8 -*-
"""
Create Time: 2024/9/10 10:03
Author: liuyigong
"""

'''
prompt的读取与解析处理

'''

from src.config.constant import *


def get_all_prompts():
    prompts = []
    for root, dirs, files in os.walk(prompts_path):
        prompts_files = [j for j in files if j.endswith(".json")]

        # log.info(prompts_files)
        for prompts_file in prompts_files:
            prompt = json.load(open(os.path.join(prompts_path, prompts_file), "r", encoding="utf8"))
            prompts.append(prompt)
    log.info(f'show all prompts: {[i["prompt_name"] for i in prompts]}')
    # log.info(prompts)
    return prompts


def filled_prompts_placeholder(prompt_id, placeholder_dict):
    prompts = get_all_prompts()
    # 遍历列表并查找具有特定 prompt_id 的元素
    prompt_js = {}

    for item in prompts:
        if item.get('prompt_id') == prompt_id:
            prompt_js = item

    if not prompt_js:
        log.error(f"missing required prompt: {prompt_id}")

    prompt_content = prompt_js['prompt_content']
    for i, (k, v) in enumerate(placeholder_dict.items()):
        prompt_content = prompt_content.replace('{{#' + str(k) + '#}}',
                                                '"' + str(v) + '"' if isinstance(v, str) else json.dumps(v, ensure_ascii=False))

    return prompt_content




# ------------------test----------------------
# prompt_content = filled_prompts_placeholder("scene_classifier", {"user_query": "外面下雨了，坐那趟车去海洋馆",
#                                                                  "scene_list": [
#                                                                      {"scene_id": "1", "scene_intention": "天气的状况"},
#                                                                      {"scene_id": "2", "scene_intention": "公交车的路线"},
#                                                                      {"scene_id": "3", "scene_intention": "其他"}],
#                                                                  "scene_intention": "出行线路规划"
#                                                                  })
# ------------------test----------------------
param_dict = {
    "parameters": [
        {"type": "object", "properties": {"att_ip": {"description": "攻击者ip", "type": "string"}}, "required": ["att_ip"]},
        {"type": "object", "properties": {"dsp_ip": {"description": "受害者ip", "type": "string"}}, "required": ["dsp_ip"]}],
    "user_query": "我的ip1.1.1.1被*******攻击了",
}
# prompt_content2 = filled_prompts_placeholder("parameter_extractor", param_dict)
# log.info(prompt_content2)
