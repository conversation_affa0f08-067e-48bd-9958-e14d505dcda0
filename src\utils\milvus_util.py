import random

from pymilvus import FieldSchema, CollectionSchema, DataType, Collection
from pymilvus import connections
from pymilvus.orm import utility
from src.config.constant import *


class MilvusClient:
    def __init__(self, host="**********", port="19530", user="root", passwd="admin@2024"):
        self.host = milvus_ip
        self.port = milvus_port
        self.user = milvus_username
        self.password = milvus_password
        self.collection = None
        self.connect()

    # 连接服务器
    def connect(self, alias="default"):
        log.info("[sc]连接数据库{}：{}".format(self.host, self.port))
        self.connection = connections.connect(alias=alias, host=self.host, port=self.port, user=self.user,
                                              password=self.password)

    # 创建集合
    def create_collection(self, collection_name="firewall_sc", fields=None, index_filed=None, description="防火墙"):
        log.info("[sc]创建集合{}".format(collection_name))
        if self.exist_collection(collection_name):
            log.info("[sc]集合{}已存在".format(collection_name))
            return
        if fields == None:
            fields = [
                FieldSchema(name="content_id", dtype=DataType.INT64, is_primary=True),
                FieldSchema(name="doc_id", dtype=DataType.INT64),
                FieldSchema(name="kbase_id", dtype=DataType.INT64),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=768)
            ]
        schema = CollectionSchema(fields=fields, description=description)
        self.collection = Collection(name=collection_name, schema=schema)
        # 创建索引
        index_params = {
            "index_type": "IVF_FLAT",
            "params": {"nlist": 100},
            "metric_type": "L2"
        }
        if index_filed == None:
            index_filed = "embedding"
        self.collection.create_index(field_name=index_filed, index_params=index_params)
        log.info("[sc]创建集合{}:{}".format(collection_name, description))

    # 插入数据
    def insert_data(self, records, collection_name="firewall_sc"):
        self.collection = Collection(name=collection_name)
        if self.collection == None:
            print("数据库连接失败")
            return
        self.collection.insert(records)
        # self.collection.flush()
        log.info("[sc]已插入集合{}共{}条记录".format(collection_name, len(records)))

    # 搜索数据
    def search_data(self, query, collection_name="firewall_sc", top_k=5, anns_field="embedding",
                    output_fields=["content_id", "doc_id", "kbase_id"], expr=None):
        log.info("[sc]搜索集合：{}，文本：{}...，限制数量：{}".format(collection_name, query[0:min(3, len(query))], top_k))
        self.collection = Collection(name=collection_name)
        query_record = query  # ToDo
        # query_record = [random.random() for _ in range(128)]
        log.info("[sc]搜索向量：{}...".format(query_record[0:min(3, len(query_record))]))
        self.collection.load()
        # 定义搜索参数
        search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
        results = self.collection.search(
            data=[query_record],  # 查询向量
            anns_field=anns_field,
            param=search_params,
            limit=top_k,
            output_fields=output_fields,
            expr=expr
        )
        return results

    #判断集合是否存在
    def exist_collection(self, collection_name):
        try:
            return utility.has_collection(using="default", collection_name=collection_name)
        except Exception:
            return False

    #删除数据
    def delete_data(self, expr=None, collection_name="firewall_sc"):
        log.info("[sc]milvus删除数据")
        if expr != None:
            self.collection = Collection(collection_name)
            self.collection.load()
            self.collection.delete(expr=expr)
            log.info("[sc]删除集合{}中：{}".format(collection_name, expr))

    # 删除集合
    def drop_collection(self, collection_name="firewall_sc"):
        self.collection = Collection(name=collection_name)
        self.collection.drop()
        log.info("[sc]删除集合{}".format(collection_name))

    # 关闭连接
    def close(self):
        if self.connection:
            self.connection.close()

milvusClient = MilvusClient()
# milvusClient.drop_collection()
# milvusClient.create_collection()

def test():
    # 连接服务器
    connections.connect("default", host="**********", port="19530", user="root", password="admin@2024")

    # 定义字段
    fields = [
        FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
        FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=128)
    ]
    # 定义集合模式
    schema = CollectionSchema(fields, description="测试集合")
    # 创建集合
    collection = Collection(name="sc_example", schema=schema)

    # 创建索引
    index_params = {
        "index_type": "IVF_FLAT",
        "params": {"nlist": 100},
        "metric_type": "L2"
    }
    collection.create_index(field_name="embedding", index_params=index_params)

    def insert_vectors():
        # 随机向量数据
        vectors = [[random.random() for _ in range(128)] for __ in range(10)]
        # 插入数据
        collection.insert([vectors])
        print("插入成功")

    def search_vector(vector, limit):
        collection.load()
        # 定义搜索参数
        search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
        results = collection.search(
            data=[vector],  # 查询向量
            anns_field="embedding",
            param=search_params,
            limit=limit,
            expr=None
        )

        for result in results:
            print(result)

    insert_vectors()

def create_test():
    ms = MilvusClient()
    # ms.drop_collection()
    ms.create_collection(collection_name="sc_test")

milvusClient = MilvusClient()

def test1():
    connections.connect("default", host="**********", port="19530", user="root", password="admin@2024")
    col = utility.has_collection(using="default", collection_name="firewall_sc1")
    if col:
        print("Yes")
    else:
        print("no")
if __name__ == '__main__':
    # test1()

    create_test()
