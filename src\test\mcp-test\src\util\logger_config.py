import logging
import os
import sys
import io
from datetime import datetime
from logging.handlers import RotatingFileHandler

# 创建日志目录
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)

# 获取当前日期作为日志文件名的一部分
current_date = datetime.now().strftime("%Y-%m-%d")
log_file = os.path.join(log_dir, f"mcp_{current_date}.log")

# 创建日志格式
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 创建文件处理器
file_handler = RotatingFileHandler(
    log_file, 
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,
    encoding='utf-8'  # 指定编码为 UTF-8
)
file_handler.setFormatter(formatter)

# 创建控制台处理器，确保使用 UTF-8 编码
if os.name == 'nt':  # Windows 系统
    # 使用 io.TextIOWrapper 包装 sys.stdout，强制使用 UTF-8 编码
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(formatter)

def get_logger(name):
    """
    获取配置好的日志记录器
    
    Args:
        name: 日志记录器名称，通常使用模块名
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    # 设置为DEBUG级别以捕获更多信息
    logger.setLevel(logging.DEBUG)
    
    # 避免重复添加处理器
    if not logger.handlers:
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    return logger

