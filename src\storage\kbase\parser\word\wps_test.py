# -*- coding: utf-8 -*-
"""
Create Time: 2024/7/18 16:01
Author: liuyigong
"""
import win32com.client
import traceback

def read_wps_document(file_path):
    try:
        # 启动WPS应用程序
        wps = win32com.client.Dispatch('KWPS.Application')
        # wps.Visible = True  # 使WPS Office的窗口在运行时可见
        # 打开文档
        doc = wps.Documents.Open(file_path)
        # 读取文档内容
        content = doc.Content.Text
        # print(content)
        headings = {}
        for para in doc.Paragraphs:
            para_text = para.Range.Text
            print(para_text)
            style_name = para.Range.ParagraphStyle.NameLocal
            style_Description = para.Range.ParagraphStyle.Description
            if style_name.startswith('标题'):
                heading_level = style_name.replace('标题', '')
                headings[para.Range.Text] = int(heading_level)
            elif style_name.startswith('正文'):
                headings[para.Range.Text] = 0
        print(headings)
        # 关闭文档
        doc.Close()
        # 退出WPS应用程序
        wps.Quit()
        return content
    except Exception as e:
        print("发生错误：")
        print(e)
        traceback.print_exc()
        # 关闭文档
        doc.Close()
        # 退出WPS应用程序
        wps.Quit()
        return None

# 使用方法
file_path = r'E:\program\python\qgpt_rag\docker\service\search\src\storage\parser\word\WPS-DOC.wps'
content = read_wps_document(file_path)
if content:
    print(content)
