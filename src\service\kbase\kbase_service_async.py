# -*- coding: utf-8 -*-
"""
Create Time: 2025/05/26 14:04
Author: liuyigong
Description: 异步知识库服务
"""
import json
import asyncio
import os
import shutil
from typing import Dict, List, Any, Optional
import time
import traceback

from src.config.constant import *
from src.utils.elasticsearch_async_util import asyncElasticSearchEngine, get_now_time
from src.utils.milvus_async_util import asyncMilvusClient
from src.service.firewall.document_service_async import milvus_delete_by_field_async
from src.utils.id_util import genNoRepeatNum10Id

async def initKbase():
    kbaseMapping = {
        "mappings": {
            "properties": {
                "name": {  # 知识库名
                    "type": "text",
                    "analyzer": "ik_max_word"
                },
                "name_keyword": {  # 知识库ID
                    "type": "keyword"
                },
                "kbase_id": {  # 知识库ID
                    "type": "keyword"
                },
                "create_time": {  # 创建时间
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "update_time": {  # 更新时间
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                }
            }
        }
    }
    
    try:
        await asyncElasticSearchEngine.create(index_name=es_kbase_index, mapping=kbaseMapping)
    except:
        log.error("【知识库索引创建出错】")
        log.error(traceback.format_exc())
        
    try:
        await asyncElasticSearchEngine.create(index_name=es_doc_index)
    except:
        log.error("【文档索引创建出错】")
        log.error(traceback.format_exc())


    try:
        await asyncMilvusClient.create_collection(collection_name=milvus_index, description="文档向量集合")
    except:
        log.error("【文档向量集合创建出错】")
        log.error(traceback.format_exc())

# 提供一个初始化函数供应用启动时调用
async def initialize_app():
    """
    应用程序初始化函数，在应用启动时调用
    """
    # asyncElasticSearchEngine.initialize()
    # asyncMilvusClient.initialize()
    await initKbase()


async def getKbaseNum():
    '''
        查询知识库数量
    :return: 数量
    '''
    query = {
        "query": {
            "match_all": {}
        }
    }
    result = await asyncElasticSearchEngine.search(query=query, index_name=es_kbase_index)
    log.info(f"【知识库数量查询结果】:{result}")
    return result["hits"]["total"]["value"]


async def createKbase(kbaseName: str, kbaseDesc: str) -> str:
    """
    异步创建知识库
    
    参数:
        kbaseName: 知识库名称
        kbaseDesc: 知识库描述
        
    返回:
        创建结果的JSON字符串
    """
    log.info(f"【知识库创建参数】:{kbaseName}: {kbaseDesc}")
    try:
        if await getKbaseNum() >= es_max_kbase_num:
            createKbaseResult = {"status": 202, "message": "超过知识库最大数量", "data": kbaseName}
            log.info(f"【知识库创建结果】:{createKbaseResult}")
            return json.dumps(createKbaseResult, ensure_ascii=False)

        # 参数验证
        if not kbaseName:
            log.info(f"【知识库创建结果】:知识库名称不能为空")
            return json.dumps({"status": 202, "message": "知识库名称不能为空", "data": {}}, ensure_ascii=False)
        
        # 检查知识库名称是否已存在
        if kbaseName.strip() == "" or await asyncElasticSearchEngine.find_field(field="name_keyword", field_value=kbaseName,
                                                                     index_name=es_kbase_index):
            log.info(f"【知识库创建结果】:知识库名称已存在")
            return json.dumps({"status": 202, "message": "知识库名称已存在", "data": {}}, ensure_ascii=False)
        
        # 生成知识库ID
        # 生成不与库中id重复的新id
        kbaseId = 0
        for genNum in range(10):
            kbaseId = genNoRepeatNum10Id()
            if not await asyncElasticSearchEngine.find_filed(field="kbase_id", field_value=kbaseId, index_name=es_kbase_index):
                break
            else:
                kbaseId = 0
        if kbaseId == 0:
            log.info(f"【知识库创建结果】:【知识库不重复id生成失败】")
            return json.dumps({"status": 202, "message": "【知识库不重复id生成失败】", "data": {}}, ensure_ascii=False)
        
        # 创建知识库文件夹
        kbase_path = os.path.join(kbaseFilePath, str(kbaseId))
        os.makedirs(kbase_path, exist_ok=True)
        log.info(f"【知识库创建文件夹成功】:{kbase_path}")

        # 创建知识库记录
        currentTime = get_now_time()
        kbaseInfo = {
            "name": kbaseName,
            "desc": kbaseDesc,
            "name_keyword": kbaseName,
            "kbase_id": kbaseId,
            "create_time": currentTime,
            "update_time": currentTime
        }

        # 插入ES
        await asyncElasticSearchEngine.insert(
            document=kbaseInfo,
            index_name=es_kbase_index
        )
        log.info(f"【知识库创建入库成功】:{kbaseId}")
        log.info(f"【知识库创建结果】: kbaseId: {kbaseId}, kbaseName: {kbaseName}")
        
        # 手动刷新 
        ## 防止因创建知识库后直接上传文件造成的知识库不存在
        await asyncElasticSearchEngine.es_data_refresh_manually(es_kbase_index)

        return json.dumps({"status": 200, "message": "创建成功", "data": {"kbase_id": kbaseId}}, ensure_ascii=False)
    except Exception as e:
        log.error(f"创建知识库失败: {str(e)}")
        log.error(traceback.format_exc())
        return json.dumps({"status": 202, "message": f"创建知识库失败: {str(e)}", "data": {}}, ensure_ascii=False)


async def updateKbase(kbaseId: str, kbaseName: str = "", kbaseDesc: str = "") -> str:
    """
    异步更新知识库信息
    
    参数:
        kbaseId: 知识库ID
        kbaseName: 知识库名称
        kbaseDesc: 知识库描述
        
    返回:
        更新结果的JSON字符串
    """
    try:
        # 记录日志
        parameters = {
            "kbaseId": kbaseId,
            "kbaseName": kbaseName,
            "kbaseDesc": kbaseDesc
        }
        log.info(f"【知识库修改参数】:{parameters}")

        # 参数验证
        if kbaseName.strip() == "" or await asyncElasticSearchEngine.find_filed(field="name_keyword", field_value=kbaseName,
                                                                     index_name=es_kbase_index):
            updateKbaseResult = {"status": 202, "message": "知识库名称已存在", "data": kbaseName}
            return json.dumps(updateKbaseResult, ensure_ascii=False)
        
        if not kbaseId:
            return json.dumps({"status": 202, "message": "知识库ID不能为空", "data": {}}, ensure_ascii=False)
        
        if kbaseId == 0 or not await asyncElasticSearchEngine.find_filed(field="kbase_id", field_value=kbaseId,
                                                                index_name=es_kbase_index):
            return json.dumps({"status": 202, "message": "知识库ID不存在", "data": {}}, ensure_ascii=False)
        
        # 准备更新名称
        query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "term": {
                                    "kbase_id": kbaseId
                                }
                            }
                        ]
                    }
                }
            }
        kbaseInfo = await asyncElasticSearchEngine.search(query=query, index_name=es_kbase_index)
        if kbaseInfo["hits"]["total"]["value"] > 0:

            source_ = kbaseInfo["hits"]["hits"][0]["_source"]
            old_kbase_name = ""
            if "name" in source_:
                old_kbase_name = source_["name"]
            old_kbase_desc = ""
            if "desc" in source_:
                old_kbase_desc = source_["desc"]

            document = {
                "doc": {
                    "name": parameters.get('kbaseName', old_kbase_name),
                    "desc": parameters.get('kbaseDesc', old_kbase_desc),
                    "name_keyword": parameters.get('kbaseName', old_kbase_name),
                    "update_time": get_now_time()
                }
            }
            await asyncElasticSearchEngine.update(document_id=kbaseInfo["hits"]["hits"][0]["_id"],
                                        document=document,
                                        index_name=es_kbase_index)
            updateKbaseResult = {"status": 200, "message": "更新成功", "data": kbaseName}
        else:
            updateKbaseResult = {"status": 202, "message": "id不存在", "data": kbaseName}


    except Exception as e:
        log.error(f"更新知识库失败: {str(e)}")
        log.error(traceback.format_exc())
        return json.dumps({"status": 202, "message": f"更新知识库失败: {str(e)}", "data": {}}, ensure_ascii=False)


async def deleteKbase(kbaseId: str) -> str:
    """
    异步删除知识库
    
    参数:
        kbaseId: 知识库ID
        
    返回:
        删除结果的JSON字符串
    """
    log.info(f"【知识库删除参数】:{kbaseId}")
    try:
        # 判断知识库id是否存在
        if kbaseId == 0 or not await asyncElasticSearchEngine.find_filed(field="kbase_id", field_value=kbaseId,
                                                              index_name=es_kbase_index):
            deleteKbaseResult = {"status": 202, "message": "知识库id不存在", "data": kbaseId}
        else:
            milvus_delete_by_field_async(field="kbase_id", field_value=kbaseId, collection_name=milvus_index)
            await asyncElasticSearchEngine.delete_by_kbase_id(kbase_id=kbaseId, index_name=es_doc_index)
            await asyncElasticSearchEngine.delete_by_kbase_id(kbase_id=kbaseId, index_name=es_kbase_index)
            currentKbaseFilePath = os.path.join(kbaseFilePath, str(kbaseId))
            if os.path.exists(currentKbaseFilePath):
                shutil.rmtree(currentKbaseFilePath, ignore_errors=True)
            deleteKbaseResult = {"status": 200, "message": "删除成功", "data": kbaseId}
    except:
        log.error(f"【知识库删除失败】")
        log.error(traceback.format_exc())
        deleteKbaseResult = {"status": 202, "message": "出错啦，请重试", "data": ""}
    log.info(f"【知识库删除结果】:{deleteKbaseResult}")
    return json.dumps(deleteKbaseResult, ensure_ascii=False)

async def getKbaseDocsNum(kbaseId) -> int:
    '''
        查询知识库的当前分段数量
    :param kbaseId: 知识库id
    :return: 分段的数量
    '''
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "kbase_id": kbaseId
                        }
                    }
                ]
            }
        },
        "collapse": {
            "field": "doc_id"
        },
        "from": 0,
        "track_total_hits": True,
        "aggs": {
            "courseAgg": {
                "cardinality": {
                    "field": "doc_id"
                }
            }
        }
    }
    listInfo = await asyncElasticSearchEngine.search(query=query, index_name=es_doc_index)
    return listInfo["aggregations"]["courseAgg"]["value"]

async def listKbase() -> str:
    """
    异步获取知识库列表
    
    返回:
        知识库列表的JSON字符串
    """
    log.info("【知识库列表查询参数】")
    try:
        # 查询所有知识库
        search_body = {
            "size": 999,
            "query": {
                "match_all": {}
            },
            "sort": [
                {"create_time": {"order": "desc"}}
            ]
        }
        
        listInfo = await asyncElasticSearchEngine.search(query=search_body, index_name=es_kbase_index)
        
        # 处理结果
        kbaseList = []
        for oneKbase in listInfo["hits"]["hits"]:
            kbaseList.append({
                "kbaseName": oneKbase["_source"]["name"],
                "kbaseId": oneKbase["_source"]["kbase_id"],
                "currentDocs": await getKbaseDocsNum(oneKbase["_source"]["kbase_id"]),
                "maxDocs": max_doc_num,
                "desc": oneKbase["_source"].get("desc", oneKbase["_source"]["name"]),
                "createTime": oneKbase["_source"]["create_time"],
                "updateTime": oneKbase["_source"]["update_time"],
            })
        log.info(f"【知识库列表结果】:{kbaseList}")
        return json.dumps({"status": 200, "message": "获取成功", "data": kbaseList}, ensure_ascii=False)
    except Exception as e:
        log.error(f"获取知识库列表失败: {str(e)}")
        log.error(traceback.format_exc())
        return json.dumps({"status": 202, "message": f"获取知识库列表失败: {str(e)}", "data": []}, ensure_ascii=False)


async def restoreFactorySettings(secret: str) -> str:
    """
    异步恢复出厂设置
    
    参数:
        secret: 密钥
        
    返回:
        恢复结果的JSON字符串
    """
    log.info(f"【恢复出厂设置参数】:{secret}")
    try:
        # 验证密钥
        if secret != "8f7c4e67001dd5439a21503dc836c4b6":
            return json.dumps({"status": 202, "message": "密钥错误", "data": {}}, ensure_ascii=False)
        
        # 删除所有知识库索引
        if await asyncElasticSearchEngine.is_exist(es_kbase_index):
            await asyncElasticSearchEngine.drop(es_kbase_index)
            log.info(f"【恢复出厂设置】:删除知识库索引成功")

        # 删除所有文档索引
        if await asyncElasticSearchEngine.is_exist(es_doc_index):
            await asyncElasticSearchEngine.drop(es_doc_index)
            log.info(f"【恢复出厂设置】:删除文档索引成功")
        
        # 清空知识库文件夹
        if os.path.exists(kbaseFilePath):
            for item in os.listdir(kbaseFilePath):
                item_path = os.path.join(kbaseFilePath, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
            log.info(f"【恢复出厂设置】:清空知识库文件夹成功")
        
        # 删除Milvus集合
        await asyncMilvusClient.drop_collection(milvus_index)
        log.info(f"【恢复出厂设置】:删除Milvus集合成功")
        
        # 重新创建索引
        await initKbase()
        log.info(f"【恢复出厂设置】:重新创建索引成功")
        
        return json.dumps({"status": 200, "message": "恢复出厂设置成功", "data": {}}, ensure_ascii=False)
    except Exception as e:
        log.error(f"恢复出厂设置失败: {str(e)}")
        return json.dumps({"status": 202, "message": f"恢复出厂设置失败: {str(e)}", "data": {}}, ensure_ascii=False)






