import os
import logging
import src.config.sys_config as sysConf
from logging.handlers import TimedRotatingFileHandler
from src.utils.singleton_util import Singleton
import traceback

'''
    日志生成工具类
'''


@Singleton
class Log:

    def __init__(self):
        '''
            实例化按照日期生成日志的日志对象、仅保留30天内的日志,默认打印级别为INFO
            info和error分文件打印，建立文件夹在主程序文件夹同级目录下
        '''
        self.log = logging.getLogger(name="rag_log_warning")
        # 判断主程序文件同级目录下有无日志(log)文件夹、无文件夹则创建
        log_path = sysConf.get_sys_path() + "/logs"
        if not os.path.exists(log_path):
            os.makedirs(log_path)

        # 日志的全局输出级别
        self.log.setLevel(logging.INFO)
        if not self.log.handlers:
            '''
                日志的格式化(按需修改配置)
                
                %(levelno)s:    打印日志级别的数值
                %(levelname)s:  打印日志级别名称
                %(pathname)s:   打印当前执行程序的路径
                %(filename)s:   打印当前执行程序名
                %(funcName)s:   打印日志的当前函数
                %(lineno)d:     打印日志的当前行号
                %(asctime)s:    打印日志的时间
                %(thread)d:     打印线程ID
                %(threadName)s: 打印线程名称
                %(process)d:    打印进程ID
                %(message)s:    打印日志信息
    
            '''
            formatter = logging.Formatter(
                "%(asctime)s [%(levelname)s] [%(threadName)s] [%(filename)s:%(lineno)d] %(message)s",
                datefmt='%Y-%m-%d %H:%M:%S')

            # 使用FileHandler输出到文件,不同级别输出到不同的文件中
            # filename日志名称、when和interval备份时间、backupCount保留文件天数
            fileHandlerDebug = TimedRotatingFileHandler(filename=log_path + "/log.log", when="MIDNIGHT", interval=1,
                                                        backupCount=600,
                                                        encoding="utf-8")

            # 日志的输出级别
            fileHandlerDebug.setLevel(level=logging.DEBUG)
            # 日志格式写入
            fileHandlerDebug.setFormatter(formatter)

            fileHandlerError = TimedRotatingFileHandler(filename=log_path + "/error.log", when="MIDNIGHT", interval=1,
                                                        backupCount=600,
                                                        encoding="utf-8")
            fileHandlerError.setLevel(level=logging.ERROR)
            fileHandlerError.setFormatter(formatter)

            # 使用StreamHandler输出到屏幕
            streamHandler = logging.StreamHandler()
            streamHandler.setFormatter(formatter)
            streamHandler.setLevel(level=logging.DEBUG)

            # 载入配置
            self.log.addHandler(fileHandlerDebug)
            self.log.addHandler(fileHandlerError)
            self.log.addHandler(streamHandler)

    def get_logging(self):
        '''
            返回日志对象，需要在执行的文件中执行log.info，否则打印的结果为工具类info所在的文件和行数

            :return: log格式化后的对象
        '''
        return self.log

    def trace_back_info(self):
        return traceback.format_exc()


# log = Log().get_logging()
