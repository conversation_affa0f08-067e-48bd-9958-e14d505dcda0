import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from collections import defaultdict


def sort_by_query_similarity(list1, list2, query):
    res_list = sort_by_query_similarity_fake(list1, list2, query)
    # res_list = sort_by_query_similarity_count(list1, list2, query)
    # res_list = sort_by_query_similarity_tf(list1, list2, query)

    return res_list

def sort_by_query_similarity_fake(list1, list2, query):
    combined_list = list1 + list2
    return combined_list

def sort_by_query_similarity_count(list1, list2, query):
    # 合并两个列表
    combined_list = list1 + list2

    # 统计每个标题的频率
    title_count = defaultdict(int)
    for item in combined_list:
        title_count[item['title']] += 1

    # 创建加权文档
    weighted_documents = []
    for item in combined_list:
        weight = title_count[item['title']]  # 使用频率作为权重
        weighted_content = (item['content'] + " ") * weight  # 重复内容以增加权重
        weighted_documents.append(weighted_content)

    # 将内容与查询一起放入列表
    all_documents = weighted_documents + [query]

    # 使用TF-IDF进行向量化
    vectorizer = TfidfVectorizer()
    tfidf_matrix = vectorizer.fit_transform(all_documents)

    # 计算余弦相似度
    cosine_similarities = cosine_similarity(tfidf_matrix[-1], tfidf_matrix[:-1])

    # 获取相似度及索引
    similarity_scores = cosine_similarities.flatten()
    sorted_indices = np.argsort(similarity_scores)[::-1]  # 从高到低排序

    # 输出结果并去重
    seen_titles = set()
    unique_sorted_combined_list = []

    for index in sorted_indices:
        title = combined_list[index]['title']
        if title not in seen_titles:
            seen_titles.add(title)
            unique_sorted_combined_list.append(combined_list[index])

    return unique_sorted_combined_list


def sort_by_query_similarity_tf(list1, list2, query):
    # 合并两个列表
    combined_list = list1 + list2

    # 提取字典字段用于相似性计算
    documents = [item['content'] for item in combined_list]

    # 将内容与查询一起放入列表
    all_documents = documents + [query]

    # 使用TF-IDF进行向量化
    vectorizer = TfidfVectorizer()
    tfidf_matrix = vectorizer.fit_transform(all_documents)

    # 计算余弦相似度
    cosine_similarities = cosine_similarity(tfidf_matrix[-1], tfidf_matrix[:-1])

    # 获取相似度及索引
    similarity_scores = cosine_similarities.flatten()
    sorted_indices = sorted(range(len(similarity_scores)), key=lambda i: similarity_scores[i], reverse=True)

    # 输出结果，并去除重复项
    seen = set()
    sorted_combined_list = []
    for index in sorted_indices:
        item_id = combined_list[index]['content_id']
        if item_id not in seen:
            seen.add(item_id)
            sorted_combined_list.append(combined_list[index])

    return sorted_combined_list

# # 示例数据
# list1 = [
#     {'title': 'Title 1', 'content': 'This is the content of the first item.', 'summary': 'Summary of item 1'},
#     {'title': 'Title 2', 'content': 'Content for the second item goes here.', 'summary': 'Summary of item 2'}
# ]
#
# list2 = [
#     {'title': 'Title 3', 'content': 'Another piece of content in the list.', 'summary': 'Summary of item 3'},
#     {'title': 'Title 4', 'content': 'More content to be considered.', 'summary': 'Summary of item 4'}
# ]
#
# # 用户查询
# query = "What is the content of item?"
#
# # 调用方法并输出结果
# sorted_results = sort_by_query_similarity(list1, list2, query)
# print("Sorted Results based on Query:")
# for result in sorted_results:
#     print(result)
