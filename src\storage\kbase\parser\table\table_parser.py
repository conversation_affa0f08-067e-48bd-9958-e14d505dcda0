import csv
import json
import xlrd
import chardet
from openpyxl import load_workbook

from src.config.constant import log


# 解析xlsx，xls,csv文件，转为json
def tableToJson(filepath: str, filename:str):
    log.info("[sc]解析表格{}".format(filepath))
    if filepath.endswith(".xlsx"):
        data = xlsx_parser(filepath)
    elif filepath.endswith("xls"):
        data = xls_parser(filepath)
    elif filepath.endswith(".csv"):
        data = csv_parser(filepath)
    else:
        return {"code": 400, "message": "格式不支持"}, None
    if data:
        result = list()
        for item in data:
            line_data = {
                "title": "",
                "book_name": filename,
                "summary": "",
                "content": json.dumps(item, ensure_ascii=False),
            }
            result.append(line_data)
        return {"code": 200, "message": "解析成功",}, result
    else:
        return {"code": 201, "message": "解析失败", "data": data}, None


# xlsx解析
def xlsx_parser(filepath):
    log.info("[sc]xlsx解析")
    try:
        workbook = load_workbook(filepath)
        sheet_names = workbook.sheetnames
        log.info("[sc]xlsx中sheet:{}".format(sheet_names))
        data = list()
        for sheet_name in sheet_names:
            sheet = workbook[sheet_name]
            num = 0
            head = list()
            for row in sheet.iter_rows(values_only=True):
                num += 1
                if num == 1:
                    for col in row:
                        head.append(col)
                else:
                    line_dict = dict()
                    for index, value in enumerate(head):
                        if index < len(row):
                            line_dict[value] = row[index]
                        else:
                            line_dict[value] = None
                    data.append(line_dict)
        workbook.close()
        log.info("[sc]xlsx解析结果：{}".format(len(data)))
        return data
    except Exception as e:
        log.info("[sc]xlxs解析错误：{}".format(e))
        return None


# xls解析
def xls_parser(filepath):
    log.info("[sc]xls解析")
    try:
        workbook = xlrd.open_workbook(filepath)
        sheet_names = workbook.sheet_names()
        log.info("[sc]xls中sheet:{}".format(sheet_names))
        data = list()
        for sheet_name in sheet_names:
            sheet = workbook.sheet_by_name(sheet_name)
            nrows = sheet.nrows
            ncols = sheet.ncols
            log.info("[sc]当前：{}共{}行{}列".format(sheet_name, nrows, ncols))
            if nrows == 0 or ncols == 0:
                log.info("[sc]数据空！")
                continue
            head = sheet.row_values(0)
            for row_id in range(1, nrows):
                row_data = sheet.row_values(row_id)
                line_dict = dict()
                for i in range(len(head)):
                    if i < len(row_data):
                        line_dict[head[i]] = row_data[i]
                    else:
                        line_dict[head[i]] = None
                data.append(line_dict)
        log.info("[sc]xls解析结果：{}".format(len(data)))
        return data
    except Exception as e:
        log.info("[sc]xlx解析错误：{}".format(e))
        return None


# csv解析
def csv_parser(filepath):
    log.info("[sc]csv解析")
    encoding = "utf8"
    try:
        with open(filepath, 'rb') as file:
            sample = file.read()
            result = chardet.detect(sample)
            encoding = result['encoding']
        with open(filepath, newline="", encoding=encoding) as file:
            csvreader = csv.reader(file)
            head = next(csvreader)
            log.info("[sc]csv头：{}".format(head))
            data = list()
            for row in csvreader:
                line_dict = dict()
                for i in range(len(head)):
                    if i < len(row):
                        line_dict[head[i]] = row[i]
                    else:
                        line_dict[head[i]] = None
                data.append(line_dict)
            log.info("[sc]csv解析结果：{}".format(len(data)))
            return data
    except Exception as e:
        log.info("[sc]csv解析错误：{}".format(e))
        return None


if __name__ == '__main__':
    # filepath = r"D:\TTS\qgpt\文本解析\data\test.xlsx"
    # data = xlsx_parser(filepath)
    # print(data)

    # filepath = r"D:\TTS\qgpt\文本解析\data\test.xls"
    # data = xls_parser(filepath)
    # print(data)

    filepath = r"D:\TTS\qgpt\文本解析\data\test.csv"
    data = csv_parser(filepath)
    print(data)
