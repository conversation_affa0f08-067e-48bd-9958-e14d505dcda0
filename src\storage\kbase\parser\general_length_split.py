import asyncio
from tqdm import tqdm
from datetime import datetime
from src.config.constant import *
from src.utils.id_util import genNoRepeatNum10Id
from src.utils.file_util import calculateMd5File
from src.calculate.embedding_server import getContentVecAsync,tokenizer
from src.utils.elasticsearch_async_util import asyncElasticSearchEngine
from src.config.constant import es_doc_index

'''
    将解析后的文档字符串进行分块入库数据构造    
'''


def doSplit(docStr, maxTokens, chunkOverlap):
    '''
        分割文本为指定大小的块
    :param docStr: 文本全内容
    :param maxTokens: 最大的tokens数
    :param chunkOverlap: 保留的上下文长度
    :return: 分割后的块list
    '''

    docStrList = []
    # 如果文本长度大于最大tokens数，小于2倍的token数
    if len(docStr) > maxTokens and len(docStr) <= 2 * maxTokens:
        docStrList.append(docStr[:int(len(docStr) * 0.5)])
        docStrList.append(docStr[int(len(docStr) * 0.5) - int(len(docStr) * 0.05):])
        return docStrList

    startPointer = 0
    # 防止bug，循环最大值为 (文本长度 / (最大tokens数 - 2 * 保留的上下文长度)) + 1
    for i in range(int(len(docStr) / (maxTokens + 2 * chunkOverlap)) + 1):
        # 如果切片开始长度+最大tokens数大于总长度，直接截取跳出循环
        if startPointer + maxTokens >= len(docStr):
            docStrList.append(docStr[startPointer:])
            break

        # 如果剩余长度小于最大token的百分之10，直接拼接到前面
        if len(docStr) - startPointer - maxTokens <= maxTokens * 0.1:
            docStrList.append(docStr[startPointer:])
            break
        docStrList.append(docStr[startPointer: startPointer + maxTokens])
        startPointer += maxTokens - chunkOverlap

    return docStrList

def doSplitByToken(docStr, maxTokens, chunkOverlap):
    '''
        分割文本为指定大小的块
    :param docStr: 文本全内容
    :param maxTokens: 最大的tokens数
    :param chunkOverlap: 保留的上下文长度
    :return: 分割后的块list
    '''

    docStrList = []
    startTokenPointer = 0
    startStrPointer = 0
    maxTokens = maxTokens - 20

    docTokens = tokenizer([docStr], padding=True, truncation=True, max_length=len(docStr),
                       return_tensors="pt")["input_ids"][0]

    # 防止bug，循环最大值为 (文本长度 / (最大tokens数 - 2 * 保留的上下文长度)) + 1
    for i in range(int(len(docTokens) / (maxTokens + 2 * chunkOverlap)) + 1):
        # 如果切片开始长度+最大tokens数大于总长度，直接截取跳出循环
        if startTokenPointer + maxTokens >= len(docTokens):
            docStrList.append(docStr[startStrPointer:])
            break

        # 如果剩余长度小于最大token的百分之10，直接拼接到前面
        if len(docTokens) - startTokenPointer - maxTokens <= maxTokens * 0.1:
            docStrList.append(docStr[startStrPointer:])
            break
        docStrList.append(docStr[startStrPointer: startStrPointer + len(
            tokenizer.decode(docTokens[startTokenPointer: startTokenPointer + maxTokens]))])
        startStrPointer += len(
            tokenizer.decode(docTokens[startTokenPointer: startTokenPointer + maxTokens - chunkOverlap]))
        startTokenPointer += maxTokens - chunkOverlap

    return docStrList


async def generateInsertDicAsync(kbaseId, fileName, filePath, docStr, maxTokens, chunkOverlap):
    '''
        异步生成入库的格式
    :param kbaseId: 知识库id
    :param fileName: 文档名称
    :param filePath: 文件路径
    :param docStr: 文档内容
    :param maxTokens: 最大token数
    :param chunkOverlap: 保留的上下文长度
    :return:
    '''
    # 获取分块后的文档内容
    docList = doSplit(docStr, maxTokens, chunkOverlap)
    
    # 构造入库数据
    esInsertList = []
    milvusInsertList = []
    contentList = []
    
    # 生成不与库中id重复的新id
    docId = 0
    for genNum in range(100):
        docId = genNoRepeatNum10Id()  # 简单的ID生成，保持同步
        # 数据库查询操作，使用异步
        if not await asyncElasticSearchEngine.find_field(field="doc_id", field_value=docId, index_name=es_doc_index):
            break
        else:
            docList = []
    
    # 计算文件MD5
    docMd5 = await asyncio.to_thread(calculateMd5File, filePath)
    
    if docList and len(docList) > 0:
        for index in range(len(docList)):
            if index % 10 == 0:
                log.info(f"【{fileName}数据生成进度】:{index}/{len(docList)}")
            
            # 构建ES文档
            esInsert = {
                "doc_name": fileName,
                "doc_name_keyword": fileName,
                "doc_md5": docMd5,
                "doc_id": docId,
                "content_id": docId * 1000000 + index,
                "title": "",
                "content": docList[index],
                "summary": "",
                "model": "length",
                "kbase_id": kbaseId,
                "create_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
            
            # 构建Milvus文档 - 简单的字典操作，保持同步
            milvusInsert = {
                "content_id": esInsert["content_id"],
                "doc_id": esInsert["doc_id"],
                "kbase_id": esInsert["kbase_id"],
            }
            
            contentList.append(docList[index])
            esInsertList.append(esInsert)
            milvusInsertList.append(milvusInsert)
    else:
        log.info("将按照长度切分的章节列表为空，无法形成入库数据。")
        return [], []
    
    log.info("【批量向量化开始】")
    # 批量向量化中间变量
    needVecList = []
    contentVecList = []
    batchSize = 5
    
    # 批量向量化
    for i in range(len(contentList)):
        if i % 10 == 0:
            log.info(f"【{fileName}批量向量化进度】:{i}/{len(contentList)}")
        
        needVecList.append(contentList[i])
        if len(needVecList) == batchSize:
            # 向量化是计算密集型操作
            batch_vectors = await getContentVecAsync(needVecList)
            contentVecList.extend(batch_vectors)
            needVecList = []
        elif len(contentList) - 1 == i:
            # 处理最后一批
            batch_vectors = await getContentVecAsync(needVecList)
            contentVecList.extend(batch_vectors)
            needVecList = []
    
    log.info("【批量向量化完成】")
    
    # 填充向量到Milvus数据 
    for i in range(len(contentVecList)):
        milvusInsertList[i]["embedding"] = contentVecList[i]

    return esInsertList, milvusInsertList
