# -*- coding: utf-8 -*-
"""
Create Time: 2024/6/20 15:02
Author: liuyigong
"""

import toml
import os
from src.utils.log_util import *
from src.config.sys_config import BASE_PATH
import json
log = Log().get_logging()
log.info("*******************************************************************************")
log.info("RAG-Document Search services starting...")
log.info("*******************************************************************************")

# 根目录
log.info(f"BASE_PATH: {BASE_PATH}")
config_path = os.path.join(BASE_PATH, "config")
dataload_path = os.path.join(BASE_PATH, os.path.join("data", "load"))
log.info(f"dataload_path: {dataload_path}")
datasave_path = os.path.join(BASE_PATH, os.path.join("data", "save"))
log.info(f"datasave_path: {datasave_path}")

prompts_path = os.path.join(dataload_path, "prompts")
sensitive_path = os.path.join(dataload_path, "sensitive")


# ---------------------需要读取的配置start------------------------------
toml_file = os.path.join(config_path, "online_qasys.toml")
# log.info(toml_file)
cfg = toml.load(toml_file)

#搜索API
es_ip = cfg["es_search"]["config"]["ip"]
es_port = cfg["es_search"]["config"]["port"]
es_kbase_index = cfg["es_search"]["config"]["kbase_index"]
es_doc_index = cfg["es_search"]["config"]["doc_index"]
es_username = cfg["es_search"]["config"]["username"]
es_password = cfg["es_search"]["config"]["password"]
# 最大知识库数量
es_max_kbase_num = cfg["es_search"]["config"]["max_kbase_num"]


#milvus配置
milvus_ip = cfg["milvus"]["config"]["ip"]
milvus_port = cfg["milvus"]["config"]["port"]
milvus_index = cfg["milvus"]["config"]["index"]
milvus_username = cfg["milvus"]["config"]["user"]
milvus_password = cfg["milvus"]["config"]["password"]


# 知识库最大文档数量
max_doc_num = cfg["chunks"]["config"]["max_doc_num"]
#分段chunks配置
chunks_max_tokens = cfg["chunks"]["config"]["max_tokens"]
chunks_overlap = cfg["chunks"]["config"]["overlap"]

# ---------------------存储上传文件的配置start-------------------------
kbaseFilePath = os.path.join(datasave_path, "files")
if not os.path.exists(kbaseFilePath):
    os.makedirs(kbaseFilePath)
log.info(f"【上传文件位置已初始化】: {kbaseFilePath}")
# ---------------------存储上传文件的配置end-------------------------

# ---------------------场景文件的配置start---------------------------
sceneFilePath = os.path.join(datasave_path, "scene")
if not os.path.exists(sceneFilePath):
    os.makedirs(sceneFilePath)
log.info(f"【场景模块文件夹位置已初始化】: {sceneFilePath}")

sceneListInfoFile = os.path.join(sceneFilePath, "scene.json")
if not os.path.exists(sceneListInfoFile):
    initSeneList = {
        "increment_id": 0,
        "scene": {}
    }
    json.dump(initSeneList, open(sceneListInfoFile, "w", encoding="utf8"), ensure_ascii=False, indent=4)
log.info(f"【场景总览文件已初始化】: {sceneListInfoFile}")
# ---------------------场景文件的配置end---------------------------

# ---------------------llm的配置start-------------------------
llmUrl = cfg["llm"]["config"]["api"]
llmModel = cfg["llm"]["config"]["model"]
# ---------------------llm的配置end---------------------------
