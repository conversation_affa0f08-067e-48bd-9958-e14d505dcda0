# -*- coding: utf-8 -*-
"""
Create Time: 2024/9/10 14:04
Author: [Your Name]
Description: 异步 LLM 工具类
"""
import json
import asyncio
from typing import Dict, Any, Optional

import httpx

from src.config.constant import *


async def getLlmAnswerAsync(prompt: str, llm_url: str = llmUrl, model: str = llmModel) -> str:
    """
    异步获取LLM的返回结果
    
    参数:
        prompt: 提示词
        llm_url: LLM服务URL
        model: 模型名称
        
    返回:
        LLM返回的回复
    """
    payload = {
        "model": model,
        "prompt": prompt,
        "stream": False
    }
    
    log.info(f"【llm异步调用问题】: {json.dumps(payload, ensure_ascii=False)}")
    
    headers = {
        'Content-Type': 'application/json',
        'Connection': 'keep-alive'
    }
    
    async with httpx.AsyncClient(verify=False) as client:
        response = await client.post(
            f"{llm_url}generate",
            headers=headers,
            json=payload
        )
        
        result = response.json()
        
        return result["response"]


async def test_async_llm():
    """测试异步LLM调用"""
    prompt = "你好，请介绍一下自己。"
    
    response = await getLlmAnswerAsync(prompt)
    print(f"LLM回复: {response}")


if __name__ == "__main__":
    asyncio.run(test_async_llm())