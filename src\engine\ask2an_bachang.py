# -*- coding: utf-8 -*-
"""
Create Time: 2024/9/10 14:04
Author: liuyigong
"""
import src.service.firewall.document_service as search_serveice
from src.calculate.reranker_server import getRerankerSources, rerank_by_score
from src.calculate.tfidf_ranker import sort_by_query_similarity
from src.service.scene.execute_service import *
from src.service.scene.scene_service import *

'''
ask to answer pipeline

'''

from src.service.kbase.kbase_service import listKbase
import re


# 修改后的intention函数，增加最终答案生成
def intention(query):
    prompt_intention = """请执行以下两个任务后返回JSON：

    【任务一】根据问题内容选择需要检索的知识库ID（kbase_id）
    【任务二】为每个选中的知识库优化query以提升检索效果

    可用知识库：
    {{#kbase_desc#}}

    判断规则：
    1. 当问题涉及基础理论概念时选择: {{#KBASE_001#}}
    2. 当需要操作流程指导时选择: {{#KBASE_002#}}
    3. 当涉及具体漏洞详情时选择: {{#KBASE_003#}}
    4. 当需要企业级解决方案时选择: {{#KBASE_004#}}

    优化建议：
    1. 若涉及漏洞情报，调整补充CVE编号格式（不要捏造）
    2. 若涉及企业方案，添加「企业级」「甲方」等限定词
    3. 技术类问题使用标准术语（如用「SSL/TLS」替代「安全加密」）

    示例：
    问题：服务器被入侵该怎么处理？
    回答：
    {
      "kbase_ids": ["{{#KBASE_002#}}", "{{#KBASE_004#}}"],
      "queries": {
        "{{#KBASE_002#}}": "网络安全应急响应流程：服务器入侵处置步骤",
        "{{#KBASE_004#}}": "服务器被入侵的企业级处置方案" 
      }
    }

    问题：AES和RSA加密的区别是什么？
    回答：
    {
    	"kbase_ids": ["{{#KBASE_001#}}"],
    	"queries": {
        "{{#KBASE_001#}}": "AES的加密方式是什么？",
        "{{#KBASE_001#}}": "RSA的加密方式是什么？" 
      }
    }

    当前问题：{{#question#}}
    请严格按上述JSON格式回答："""

    kbase_data = json.loads(listKbase()).get("data")
    kbase_desc = [{
        "kbaseName": item["kbaseName"],
        "kbaseId": item["kbaseId"],
        "desc": item["desc"]
    } for item in kbase_data]

    KBASE_001 = ""
    KBASE_002 = ""
    KBASE_003 = ""
    KBASE_004 = ""
    for i in kbase_desc:
        if "basic" in i['kbaseName']:
            KBASE_001 = i['kbaseId']
        if "policy" in i['kbaseName']:
            KBASE_002 = i['kbaseId']
        if "loophole" in i['kbaseName']:
            KBASE_003 = i['kbaseId']
        if "solutions" in i['kbaseName']:
            KBASE_004 = i['kbaseId']

    placeholder_dict = {
        "question": query,
        "kbase_desc": kbase_desc,
        "KBASE_001": KBASE_001,
        "KBASE_002": KBASE_002,
        "KBASE_003": KBASE_003,
        "KBASE_004": KBASE_004
    }
    log.info(f"placeholder_dict:{placeholder_dict}")

    for i, (k, v) in enumerate(placeholder_dict.items()):
        prompt_intention = prompt_intention.replace('{{#' + str(k) + '#}}',
                                                    '"' + str(v) + '"' if isinstance(v, str)
                                                    else json.dumps(v, ensure_ascii=False))
    log.info("完整的意图识别prompt为：")
    log.info(prompt_intention)
    return prompt_intention


def askMe(query, data=dict()):
    try:
        session_id = data.get('session_id', '')
        log.info('\n')
        log.info('\n')
        log.info('\n')
        log.info('-----------------------------------------------------------------------------------')
        log.info('askMe for the sessionId:%s, query:%s, time:%s'
                 % (str(session_id), str(query), str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))))
        log.info('\n')
        log.info((f'获得POST-data: {data}'))

        prompt_total = intention(query=query)

        # 调用llm生成结果
        response = generate_text(prompt=prompt_total)
        print(response)
        llm_result = json.loads(response)
        log.info(f"大模型返回： {llm_result}")

        # 处理意图识别结果并获取检索内容
        context = process_intention_result(llm_result, query)

        # 生成最终答案
        final_answer, retrieved_info = generate_final_answer(context)
        final_answer += "\n =============================== \n"
        final_answer += retrieved_info

        return {'code': 200, "msg": "success", "data": {"llm_ans": final_answer, "doc_source": []}}
    except Exception as e:
        log.error(Log().trace_back_info())
        return {'code': 202, "msg": "生成答案时出现错误。", "data": {}}


def search_docs(query, data=dict()):
    #  ---------------------------------------   获取所有参数 -------------------------------------------
    try:
        session_id = data.get('session_id', '')
        kb_list = data.get('kb_list', '')
        kb_list = kb_list.split(",")
        kb_list = [int(kb.strip()) for kb in kb_list if kb.strip() != '']
        #  ---------------------------------------   敏感词过滤  -------------------------------------------
        pass

        #  ---------------------------------------   获取外接信息 -------------------------------------------
        #  ----------- es 搜索 -------------------
        es_search_result_list = []
        es_res = search_serveice.es_search(query=query, index_name=es_doc_index, kbase_ids=kb_list)
        if es_res:
            es_search_result_list = es_res
        log.info(f"es 召回相关信息共{len(es_search_result_list)}条")
        log.info(f"es 召回相关content_id为{[i['content_id'] for i in es_search_result_list]}")
        log.info(
            f'es-top3 的标题分别为：{[i["title"] for i in es_search_result_list[0:min(3, len(es_search_result_list))]]}')
        #  ----------- Milvus 搜索 -------------------
        milivs_search_result_list = []
        milvus_res = search_serveice.milvus_search(query=query, index_name=es_doc_index, collection_name=milvus_index,
                                                   kbase_ids=kb_list)
        if milvus_res:
            milivs_search_result_list = milvus_res
        log.info(f"milvus 召回相关信息共{len(es_search_result_list)}条")
        log.info(f"milvus 召回相关content_id为{[i['content_id'] for i in milivs_search_result_list]}")
        log.info(
            f'milvus-top3 的标题分别为：{[i["title"] for i in milivs_search_result_list[0:min(3, len(milivs_search_result_list))]]}')
        #  ----------- 两个搜索结果合并和粗排 -------------------
        res_list = sort_by_query_similarity(es_search_result_list, milivs_search_result_list, query)

        log.info(f'粗排-top3 的标题分别为：{[i["title"] for i in res_list[0:min(3, len(res_list))]]}')
        #  ----------- 粗排列表进入bce-rerank模型重排 -------------------
        log.info(f'跳过重排序')
        sorted_res_list = res_list

        # log.info(f'粗排后的数据取中值以上部分')
        # if len(res_list) > 6:
        #     res_list = [i for i in res_list[:len(res_list) // 2]]
        # content_list = [i["content"] for i in res_list]
        # score_list = getRerankerSources(query=query, contentList=content_list)
        # sorted_res_list = rerank_by_score(res_list, score_list)
        #  ----------- 重排结果最终处理 -------------------
        n = 3
        top_n = sorted_res_list[0:min(n, len(sorted_res_list))]
        # top_n = [
        #     {**item, 'content': sensitive_filter(item['content'])}
        #     for item in top_n
        # ]  ## 解包并每个元素中的content字段用sensitive_filter()方法过滤一遍
        #
        # log.info(f"重排序后相关content_id为{[i['content_id'] for i in sorted_res_list]}")
        log.info(f'top{n} 的标题分别为：{[i["title"] for i in top_n]}')
        #
        #  ---------------------------------------   富化 -------------------------------------------
        enrichment_ans = "跳过模型富化"
        last_res_list = [{i["title"]: i["content"]} for i in top_n]
        # last_res = "|".join(last_res_list)
        # last_res = last_res[:min(3000, len(last_res))]
        return {"data": last_res_list}
    except Exception as e:
        log.error(Log().trace_back_info())
        return {"data": {}}
    #


import concurrent.futures
from typing import List, Dict
import json


def process_intention_result(llm_result: Dict, original_query: str) -> Dict:
    """
    处理意图识别结果，多线程调用askMeNoLLM并汇总结果

    参数:
        llm_result: 意图识别结果，包含kbase_ids和queries
        original_query: 用户的原始查询

    返回:
        包含所有检索结果和原始查询的字典
    """
    results = []

    # 准备多线程任务
    def fetch_kbase_data(query: str, kb_list: List[str]) -> Dict:
        data = {
            'session_id': '',
            'kb_list': ','.join(kb_list),
            'query': query
        }
        return search_docs(query, data)

    # 使用线程池并发执行
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = []

        # 为每个知识库和对应的查询创建任务
        for kbase_id, query in llm_result['queries'].items():
            futures.append(
                executor.submit(
                    fetch_kbase_data,
                    query=query,
                    kb_list=[kbase_id]  # 单个知识库ID
                )
            )

        # 等待所有任务完成
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                if result and result.get('data'):
                    results.extend(result['data'])
            except Exception as e:
                log.error(f"Error fetching kbase data: {e}")

    seen = set()
    unique_results = [x for x in results if frozenset(x.items()) not in seen and not seen.add(frozenset(x.items()))]
    return {
        'original_query': original_query,
        'retrieved_results': unique_results
    }


def generate_final_answer(context: Dict):
    """
    根据检索结果生成最终答案

    参数:
        context: 包含原始查询和检索结果的字典

    返回:
        大模型生成的最终答案
    """
    prompt_template = """
    你是个聪明的助手。请总结知识库的内容来回答问题。请列出知识库中的数据并详细回答。
    请综合知识库信息，用中文给出清晰、专业的回答。如果信息不足，当所有的知识库内容都与问题无关时，你的答案必须包括“你正在寻找的答案在知识库中找不到！”.
    
    以下是知识库：
    {retrieved_info}
    以上就是知识库。
    
    已下是用户问题:
    {original_query}
    以上是用户问题
    """

    prompt_template = '''
    角色：你是个聪明的助手。你的名字是Q-RAG。
    任务：从知识库中总结信息，回答用户的问题。
    要求和限制：
    -尽可能用知识库中的与问题相关的信息和原文回答。
    -不要编造，尤其是数字。
    -如果来自知识的信息与用户的问题无关，只需说：对不起，没有提供相关信息。
    -回答markdown格式的文本。
    -回答用户的问题的语言。
    -不要编造，尤其是数字。
    
    来自知识库的信息
    {retrieved_info}
    以上是来自知识库的信息。
    
    已下是用户问题:
    {original_query}
    以上是用户问题
    '''

    # 整理检索到的信息
    retrieved_info = ""
    for i, result in enumerate(context['retrieved_results'], 1):
        retrieved_info += f"\n【来源{i}】\n"
        # retrieved_info += f"LLM答案: {result.get('llm_ans', '无')}\n"
        retrieved_info += f"文档来源: {json.dumps(result, ensure_ascii=False)}\n"

    prompt = prompt_template.format(
        original_query=context['original_query'],
        retrieved_info=retrieved_info
    )
    log.info(f"调用大模型生成最终答案: {prompt}")
    # 调用大模型生成最终答案
    try:
        final_answer = generate_text(prompt=prompt)
        return final_answer, retrieved_info
    except Exception as e:
        log.error(f"Error generating final answer: {e}")
        return "抱歉，生成答案时出现错误。", None


def generate_text(model: str = "deepseek-r1:32b", prompt: str = "", stream: bool = False):
    """
    调用生成文本的API

    参数:
        model (str): 使用的模型名称
        prompt (str): 输入的提示文本
        stream (bool): 是否使用流式输出，默认为False

    返回:
        API的响应结果
    """
    if not prompt:
        log.info("prompt 为空")
        return

    url = "http://kubemlsvc.qianxin-inc.cn:80/svc-d7vd819rjkxh/api/generate"

    payload = json.dumps({
        "model": model,
        "prompt": prompt,
        "stream": stream
    })

    headers = {
        'Content-Type': 'application/json',
        'Connection': 'keep-alive'
    }

    try:
        response = requests.post(url, headers=headers, data=payload)
        response.raise_for_status()  # 检查请求是否成功
        # print(response)
        response_text = response.json().get("response", "")

        # 使用正则表达式移除<think>标签及其内容（包括换行符）
        cleaned_response = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL)

        return cleaned_response.strip().replace("```json", "").replace("```", "")
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return {"error": str(e)}
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return {"error": "Invalid JSON response"}
