# -*- coding: utf-8 -*-
"""
Create Time: 2024/9/10 14:04
Author: liuyigong
Description: 异步文档服务
"""
import asyncio
from typing import Dict, List, Any, Optional, Union

from src.config.constant import log, es_doc_index, milvus_index
from src.utils.elasticsearch_async_util import asyncElasticSearchEngine
from src.utils.milvus_async_util import asyncMilvusClient
from src.calculate.embedding_server import getQueryVec, getQueryVecAsync

# 异步防火墙入库
async def es_insert_data_async(es_data: List[Dict], milvus_data: List[Dict], 
                              index_name: str = "firewall_sc", 
                              collection_name: str = "firewall_sc") -> Dict:
    """
    异步将数据插入ES和Milvus
    
    参数:
        es_data: ES数据
        milvus_data: Milvus数据
        index_name: ES索引名称
        collection_name: Milvus集合名称
        
    返回:
        插入结果
    """
    # es入库
    log.info("[sc]es异步入库")
    
    # 检查索引是否存在
    if not await asyncElasticSearchEngine.is_exist(index_name):
        log.error(f"[sc]es库{index_name}索引不存在")
        return {"code": 202, "message": f"{index_name}索引不存在"}
    
    is_success = True
    ids = []
    
    # 插入ES数据
    for item in es_data:
        if not item.get("doc_name", None) or not item.get("doc_id", None) or \
           not item.get("content_id", None) or not item.get("content", None) or \
           not item.get("kbase_id", None) or not item.get("create_time", None):
            log.error(f"[sc]{item}中字段缺失")
            is_success = False
            break
        
        try:
            result = await asyncElasticSearchEngine.insert(item, index_name=index_name)
            log.info(f"[sc]es入库: {item}, 结果: {result}")
            if not result:
                log.error("[sc]es入库失败：es连接失败")
                is_success = False
                break
            ids.append(result["_id"])
        except Exception as e:
            log.error(f"[sc]es入库异常: {str(e)}")
            is_success = False
            break
    
    # 如果ES入库失败，进行回滚
    if not is_success:
        log.info("[sc]es入库失败，准备回滚")
        await es_rollback_async(ids, index_name)
        return {"code": 201, "message": "es入库失败"}
    
    # milvus入库
    log.info("[sc]milvus异步入库")
    try:
        await asyncMilvusClient.insert_data(milvus_data, collection_name=collection_name)
    except Exception as e:
        log.info(f"[sc]milvus入库失败：{str(e)}")
        await es_rollback_async(ids, index_name)
        return {"code": 201, "message": "milvus入库失败"}

    return {"code": 200, "message": "入库成功", "data": {"es": len(es_data), "milvus": len(milvus_data)}}

# es回滚
async def es_rollback_async(ids: List[str], index_name: str = "firewall_sc") -> Dict:
    """
    异步回滚ES数据
    
    参数:
        ids: 数据ID列表
        index_name: 索引名称
        
    返回:
        回滚结果
    """
    log.info("[sc]es回滚")
    try:
        result = await asyncElasticSearchEngine.delete(ids, index_name=index_name)
        if not result:
            log.error("[sc]回滚失败")
            return {"code": 204, "message": "回滚失败"}
        log.info(f"[sc]回滚成功：{result}")
        return {"code": 200, "message": "回滚成功"}
    except Exception as e:
        log.error(f"[sc]回滚报错：{str(e)}")
        return {"code": 204, "message": "回滚失败"}

# es根据字段删除数据
async def es_delete_by_field_async(field: str, field_value: str, index_name: str = "firewall_sc") -> Dict:
    """
    异步根据字段删除ES数据
    
    参数:
        field: 字段名
        field_value: 字段值
        index_name: 索引名称
        
    返回:
        删除结果
    """
    log.info(f"[sc]es异步删除：{field}={field_value}")
    try:
        await asyncElasticSearchEngine.delete_by_field_batch(field, field_value, index_name)
    except Exception as e:
        log.error(f"[sc]es删除报错：{str(e)}")
        return {"code": 201, "message": "删除失败"}
    
    return {"code": 200, "message": "删除成功"}

# es刷新
async def es_data_refresh_manually_async(index_name: str) -> Dict:
    """
    异步刷新ES索引
    
    参数:
        index_name: 索引名称
        
    返回:
        刷新结果
    """
    try:
        res = await asyncElasticSearchEngine.data_refresh(index_name)
        if res:
            return {"code": 200, "message": "刷新成功"}
        return {"code": 201, "message": "刷新失败"}
    except Exception as e:
        log.error(f"[sc]es刷新报错：{str(e)}")
        return {"code": 201, "message": "刷新失败"}

# milvus根据字段删除数据
async def milvus_delete_by_field_async(field: str, field_value: str, collection_name: str) -> Dict:
    """
    异步根据字段删除Milvus数据
    
    参数:
        field: 字段名
        field_value: 字段值
        collection_name: 集合名称
        
    返回:
        删除结果
    """
    log.info(f"[sc]milvus异步删除：{field}={field_value}")
    query = f"{field} in [{field_value}]"
    try:
        await asyncMilvusClient.delete_data(expr=query, collection_name=collection_name)
    except Exception as e:
        log.error(f"[sc]milvus删除报错：{str(e)}")
        return {"code": 201, "message": "删除失败"}
    
    return {"code": 200, "message": "删除成功"}

# es搜索
async def es_search_async(query: str, index_name: str = "firewall_sc", 
                         kbase_ids: Optional[List[int]] = None,
                         doc_ids: Optional[List[str]] = None, 
                         content_ids: Optional[str] = None) -> List[Dict]:
    """
    异步ES搜索
    
    参数:
        query: 搜索查询
        index_name: 索引名称
        kbase_ids: 知识库ID列表
        doc_ids: 文档ID列表
        content_ids: 内容ID
        
    返回:
        搜索结果
    """
    log.info("[sc]es异步搜索")
    log.info(f"[sc]搜索内容: {query}")
    
    response = await asyncElasticSearchEngine.search_async(
        query, 
        top_k=10, 
        index_name=index_name,
        kbase_ids=kbase_ids, 
        document_ids=doc_ids,
        content_ids=content_ids
    )
    
    if not response:
        log.warning("[sc]es搜索结果为空")
        return []
    
    hits = response.get("hits", {}).get("hits", [])
    data = []
    
    for hit in hits:
        data.append(hit.get("_source", {}))
    
    return data

# milvus搜索
async def milvus_search_async(query: Optional[str] = None, 
                             query_vec: Optional[List[float]] = None, 
                             index_name: str = "firewall_sc", 
                             collection_name: str = "firewall_sc", 
                             kbase_ids: Optional[List[int]] = None,
                             doc_ids: Optional[List[str]] = None) -> List[Dict]:
    """
    异步Milvus搜索
    
    参数:
        query: 搜索查询
        query_vec: 查询向量
        index_name: ES索引名称
        collection_name: Milvus集合名称
        kbase_ids: 知识库ID列表
        doc_ids: 文档ID列表
        
    返回:
        搜索结果
    """
    log.info("[sc]milvus异步搜索")
    
    if not query and not query_vec:
        log.info("[sc]milvus搜索内容为空")
        return []
    
    if not query_vec:
        # 使用异步方法获取查询向量
        query_vec = await getQueryVecAsync(query)
    
    log.info(f"[sc]搜索向量：{query_vec[0:min(3, len(query_vec))]}...")
    
    # 构建过滤表达式
    expr = None
    if kbase_ids and doc_ids:
        expr = f"kbase_id in {kbase_ids} and doc_id in {doc_ids}"
    elif kbase_ids:
        expr = f"kbase_id in {kbase_ids}"
    elif doc_ids:
        expr = f"doc_id in {doc_ids}"
    
    log.info(f"[sc]expr={expr}")
    
    # 执行搜索
    response = await asyncMilvusClient.search_data(
        query=query_vec, 
        collection_name=collection_name, 
        top_k=10, 
        expr=expr,
        # output_fields=["content_id", "doc_id", "kbase_id"]
    )
    
    data = []
    
    if not response:
        log.error(f"[sc]milvus搜索失败: {response}")
        return data
    
    items = response[0]
    
    for item in items:
        body = {
            "query": {
                "term": {"content_id": item.entity.content_id}
            }
        }
        
        response_search = await asyncElasticSearchEngine.search(query=body, index_name=index_name)
        if not response_search:
            log.error(f"[sc]es搜索失败: {body}, index_name={index_name}")
            continue
        
        hits = response_search.get("hits", {}).get("hits", [])
        
        for hit in hits:
            data.append(hit.get("_source", {}))
    
    return data

# 检查文档是否存在
async def check_doc_exists_async(doc_id: str, index_name: str = "firewall_sc") -> bool:
    """
    异步检查文档是否存在
    
    参数:
        doc_id: 文档ID
        index_name: 索引名称
        
    返回:
        是否存在
    """
    log.info(f"[sc]检查文档是否存在: {doc_id}")
    try:
        query = {
            "query": {
                "term": {
                    "doc_id": doc_id
                }
            }
        }
        
        response = await asyncElasticSearchEngine.search(query, index_name)
        if not response:
            log.error(f"[sc]es搜索失败: {query}, index_name={index_name}")
            return False
        
        hits = response.get("hits", {}).get("hits", [])
        
        return len(hits) > 0
    except Exception as e:
        log.error(f"检查文档存在失败: {str(e)}")
        return False

# 删除文档
async def delete_doc_async(doc_id: str, index_name: str = "firewall_sc", collection_name: str = "firewall_sc") -> Dict:
    """
    异步删除文档
    
    参数:
        doc_id: 文档ID
        index_name: ES索引名称
        collection_name: Milvus集合名称
        
    返回:
        删除结果
    """
    log.info(f"[sc]删除文档: {doc_id}")
    try:
        # 删除ES中的文档
        es_result = await es_delete_by_field_async("doc_id", doc_id, index_name)
        if es_result.get("code") != 200:
            return es_result
        log.info("[sc]es删除成功")
        
        # 删除Milvus中的向量
        milvus_result = await milvus_delete_by_field_async("doc_id", doc_id, collection_name)
        if milvus_result.get("code") != 200:
            return milvus_result
        log.info("[sc]milvus删除成功")
        
        return {
            "code": 200,
            "message": "删除成功",
            "data": {}
        }
    except Exception as e:
        log.error(f"删除文档失败: {str(e)}")
        return {
            "code": 202,
            "message": f"删除文档失败: {str(e)}",
            "data": {}
        }

# 混合搜索
async def search_async(query: str, 
                      kbase_ids: Optional[List[str]] = None, 
                      doc_ids: Optional[List[str]] = None, 
                      search_type: str = "hybrid", 
                      top_k: int = 10) -> Dict:
    """
    异步混合搜索
    
    参数:
        query: 搜索查询
        kbase_ids: 知识库ID列表
        doc_ids: 文档ID列表
        search_type: 搜索类型 (hybrid, es, milvus)
        top_k: 返回结果数量
        
    返回:
        搜索结果
    """
    try:
        results = []
        
        if search_type == "hybrid" or search_type == "es":
            # ES搜索
            es_results = await es_search_async(query, es_doc_index, kbase_ids, doc_ids)
            results.extend(es_results)
        
        if search_type == "hybrid" or search_type == "milvus":
            # 获取查询向量
            query_vec = await getQueryVecAsync(query)
            
            # Milvus搜索
            milvus_results = await milvus_search_async(
                query=None, 
                query_vec=query_vec, 
                index_name=es_doc_index, 
                collection_name=milvus_index, 
                kbase_ids=kbase_ids, 
                doc_ids=doc_ids
            )
            
            # 合并结果，去重
            content_ids = set()
            for item in results:
                content_ids.add(item.get("content_id"))
            
            for item in milvus_results:
                if item.get("content_id") not in content_ids:
                    results.append(item)
                    content_ids.add(item.get("content_id"))
        
        # 限制返回结果数量
        results = results[:top_k]
        
        return {
            "code": 200,
            "message": "搜索成功",
            "data": results
        }
    except Exception as e:
        log.error(f"搜索失败: {str(e)}")
        return {
            "code": 202,
            "message": f"搜索失败: {str(e)}",
            "data": []
        }

# 根据知识库ID删除数据
async def delete_by_kbase_id_async(kbase_id: str, 
                                  index_name: str = "firewall_sc", 
                                  collection_name: str = "firewall_sc") -> Dict:
    """
    异步根据知识库ID删除数据
    
    参数:
        kbase_id: 知识库ID
        index_name: ES索引名称
        collection_name: Milvus集合名称
        
    返回:
        删除结果
    """
    try:
        # 删除ES中的数据
        es_result = await es_delete_by_field_async("kbase_id", kbase_id, index_name)
        if es_result.get("code") != 200:
            return es_result
        
        # 删除Milvus中的数据
        milvus_result = await milvus_delete_by_field_async("kbase_id", kbase_id, collection_name)
        if milvus_result.get("code") != 200:
            return milvus_result
        
        return {
            "code": 200,
            "message": "删除成功",
            "data": {}
        }
    except Exception as e:
        log.error(f"删除知识库数据失败: {str(e)}")
        return {
            "code": 202,
            "message": f"删除知识库数据失败: {str(e)}",
            "data": {}
        }

async def test_document_service_async():
    """测试异步文档服务"""
    # 测试搜索
    result = await search_async("测试查询", top_k=5)
    print(f"搜索结果: {result}")
    
    # 测试删除
    delete_result = await delete_doc_async("test_doc_id")
    print(f"删除结果: {delete_result}")
    
    print("测试完成")


if __name__ == "__main__":
    asyncio.run(test_document_service_async())




