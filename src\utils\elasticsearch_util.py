from elasticsearch import Elasticsearch
from datetime import datetime
from src.config.constant import *


class ElasticSearchEngine:
    def __init__(self):
        self.host = es_ip
        self.port = es_port
        self.user = es_username
        self.pwd = es_password
        log.info("[sc]连接es:{}:{} user:{} pwd:{}".format(self.host, self.port, self.user, self.pwd))
        if self.user and self.pwd:
            self.es = Elasticsearch(self.host + ":" + str(self.port), basic_auth=(self.user, self.pwd))
        else:
            self.es = Elasticsearch(self.host + ":" + str(self.port))
        if self.es.ping():
            # print("success")
            log.info("[sc]es连接成功")
        else:
            log.info("[sc]es连接失败")

    # es重新链接
    def reconnect(self):
        self.host = es_ip
        self.port = es_port
        self.user = es_username
        self.pwd = es_password
        log.info("[sc]连接es:{}:{} user:{} pwd:{}".format(self.host, self.port, self.user, self.pwd))
        if self.user and self.pwd:
            self.es = Elasticsearch(self.host + ":" + str(self.port), basic_auth=(self.user, self.pwd))
        else:
            self.es = Elasticsearch(self.host + ":" + str(self.port))

    # 创建索引和字段+同义词
    # 版本问题，暂不支持
    def create_sc(self):
        settings = {
            "settings": {
                "index": {
                    "analysis": {
                        "analyzer": {
                            "synonym_analyzer": {
                                "tokenizer": "standard",
                                "filter": ["synonym_filter"]
                            }
                        }
                    },
                    "filter": {
                        "synonym_filter": {
                            "type": "synonym",
                            "synonyms": ["沈杨,沈老师,shenyang"]
                        }
                    }
                },
            }
        }
        mappings = {
            "mappings": {
                "properties": {
                    "name": {
                        "type": "text",
                        "analyzer": "synonym_analyzer"
                    }
                }
            }
        }
        self.es.indices.create(index="test_sc", settings=settings, mappings=mappings)

    # 创建索引和字段
    def create(self, index_name="firewall_sc", mapping=None):
        if self.es.indices.exists(index=index_name):
            log.info("[sc]{}索引已存在".format(index_name))
            return
        if mapping == None:
            mapping = {
                "mappings": {
                    "properties": {
                        "doc_name": {  # 文档名
                            "type": "text",
                            "analyzer": "ik_max_word"
                        },
                        "doc_name_keyword": {  # 文档名判重字段
                            "type": "keyword"
                        },
                        "doc_md5": {  # 文档判重机制
                            "type": "keyword"
                        },
                        "doc_id": {
                            "type": "keyword"
                        },
                        "content_id": {  # 段落ID
                            "type": "keyword"
                        },
                        "title": {  # 级联标题
                            "type": "text",
                            "analyzer": "ik_max_word"
                        },
                        "content": {  # 段落内容
                            "type": "text",
                            "analyzer": "ik_max_word"
                        },
                        "summary": {  # 段落摘要
                            "type": "text",
                            "analyzer": "ik_max_word"
                        },
                        "model": {  # 分段方式
                            "type": "keyword"
                        },
                        "kbase_id": {  # 关联知识库ID
                            "type": "keyword"
                        },
                        "create_time": {  # 创建时间
                            "type": "date",
                            "format": "yyyy-MM-dd HH:mm:ss"
                        }
                    }
                }
            }
        # 创建索引
        self.es.indices.create(index=index_name, body=mapping)
        log.info("[sc]{}索引创建成功".format(index_name))

    # 查询
    def search(self, query, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es连接断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        return self.es.search(index=index_name, body=query)

    # firewall查询
    def search_firewall(self, text, top_k=5, index_name="firewall_sc",
                        kbase_ids=None, document_ids=None, content_ids=None):
        if len(text) == 0 and not content_ids:
            log.info("[sc]搜素内容为空")
            return None
        #
        # query = {
        #     "query": {
        #         "multi_match": {
        #             "query": text,
        #             "fields": ["content", "summary", "title"]
        #         }
        #     },
        #     "size": top_k
        # }

        #
        filter = []
        if content_ids:
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "match": {
                                    "content_id": content_ids
                                }
                            }
                        ]
                    }
                },
                "size": top_k
            }
        else:
            if document_ids and kbase_ids:
                filter.append({
                    "terms": {
                        "doc_id": document_ids
                    }
                })
                filter.append({
                    "terms": {
                        "kbase_id": kbase_ids
                    }
                })
            elif document_ids:
                filter.append({
                    "terms": {
                        "doc_id": document_ids
                    }
                })
            elif kbase_ids:
                filter.append({
                    "terms": {
                        "kbase_id": kbase_ids
                    }
                })
            query = {
                "query": {
                    "bool": {
                        "must": [{
                            "multi_match": {
                                "query": text,
                                "fields": ["content", "summary", "title", "model"]
                            }

                        }],
                        "should": [
                            {
                                "match": {
                                    "title": text
                                }
                            }
                        ],
                        "filter": filter
                    }
                },
                "size": top_k
            }


        log.info(query)
        return self.es.search(index=index_name, body=query)

    # 入库
    def insert(self, document, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        # log.info("[sc]索引{}入库：{}".format(index_name, document))
        log.info("[sc]索引{}入库".format(index_name))
        return self.es.index(index=index_name, body=document)

    # 添加同义词
    def add_synonyms(self, synonyms, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        log.info("[sc]同义词添加：{}".format(synonyms))
        body = {
            "synonyms": {
                "type": "synonym",
                "synonyms": synonyms
            }
        }
        # synonyms_set = []
        # for index, item in enumerate(synonyms):
        #     synonyms_set.append({"id": "synonym-{}".format(index), "synonyms": item})

        # self.es.synonyms.
        # return self.es.synonyms.put_synonym(id="sc_synonyms", synonyms_set=synonyms_set)
        return self.es.indices.put_settings(index=index_name, body={"analysis": {"filter": body}})

    # 判断某字段存在
    def find_filed(self, field, field_value, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        log.info("[sc]查重{}={}".format(field, field_value))
        query = {
            "query": {
                "term": {
                    field: field_value
                }
            }
        }
        response = self.es.search(index=index_name, body=query)
        log.info("[sc]查重结果：{}".format(response))
        total = response.get("hits", {}).get("total", {}).get("value", 0)
        # print(response)
        if total > 0:
            return True
        else:
            return False

    # 更新
    def update(self, document_id, document, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        return self.es.update(index=index_name, id=document_id, body=document)

    # 删除数据
    def delete(self, document_id, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        result = []
        if isinstance(document_id, str):
            result.append(self.es.delete(index=index_name, id=document_id))
        elif isinstance(document_id, list):
            for item in document_id:
                result.append(self.es.delete(index=index_name, id=item))
        else:
            log.info("[sc]删除格式不匹配")
            return {"code": 202, "message": "格式错误", "data": []}
        return {"code": 200, "message": "删除成功", "data": result}

    # 批量删除指定字段所有数据
    def delete_by_filed_batch(self, field, value, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        query = {
            "query": {
                "term": {
                    field: value
                }
            }
        }
        log.info("[sc]删除{}={}数据".format(field, value))
        return self.es.delete_by_query(index=index_name, body=query)

    # 删除指定kbase所有数据
    def delete_by_kbase_id(self, kbase_id, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        query = {
            "query": {
                "term": {
                    "kbase_id": kbase_id
                }
            }
        }
        log.info("[sc]删除kbase_id={}数据".format(kbase_id))
        return self.es.delete_by_query(index=index_name, body=query)

    # 删除索引
    def drop(self, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        if self.es.indices.exists(index=index_name):
            self.es.indices.delete(index=index_name)
            log.info("[sc]{}索引删除成功".format(index_name))
        else:
            log.info("[sc]{}索引不存在".format(index_name))

    # 手动刷新
    def data_refresh(self, index_name):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        self.es.indices.refresh(index=index_name)
        return "ok"

    # 统计数据量
    def count(self, index_name="firewall_sc"):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        return self.es.count(index=index_name)

    # 判断某索引存在
    def is_exist(self, index_name):
        return self.es.indices.exists(index=index_name)

    def reindex(self, source_name, dest_name):
        if not self.es.ping():
            log.info("[sc]es断开")
            self.reconnect()
            if not self.es.ping():
                log.info("[sc]es重新连接失败")
                return None
        body = {
            "source": {
                "index": source_name
            },
            "dest": {
                "index": dest_name
            }
        }
        self.es.reindex(body=body)
        log.info("[sc]数据迁移{}->{}".format(source_name, dest_name))


elasticSearchEngine = ElasticSearchEngine()


def get_now_time():
    now = datetime.now()
    return now.strftime("%Y-%m-%d %H:%M:%S")


def test():
    elasticSearchEngine = ElasticSearchEngine()

    def get_es_engine(host, port, user=None, pwd=None):
        if user and pwd:
            es = Elasticsearch(host + ":" + str(port), basic_auth=(user, pwd))
        else:
            es = Elasticsearch(host + ":" + str(port))
        return es

    # es = Elasticsearch(["http://**********:9200"])
    es = get_es_engine("http://**********", 9200, "elastic", "admin@2024")

    if es.ping():
        print("success")
    else:
        print("failed")
        exit()

    # 定义索引和字段类型
    index_name = "my_index"
    mapping = {
        "mappings": {
            "properties": {
                "doc_name": {"type": "text"},  # 文档名
                "content_id": {"type": "keyword"},  # 段落ID
                "title": {"type": "text"},  # 级联标题
                "content": {"type": "text"},  # 段落内容
                "summary": {"type": "text"},  # 段落摘要
                "kbase_id": {"type": "keyword"},  # 关联知识库ID
                "create_time": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}  # 创建时间
            }
        }
    }

    # 创建索引
    # es.indices.create(index=index_name, body=mapping)

    # 定义文档
    doc = {
        "doc_name": "小趴菜",
        "content_id": "dl001",
        "title": "第一段_第一部分",
        "content": "那女孩对我说，说我是一个小偷，偷走她的心。",
        "summary": "偷心贼",
        "kbase_id": "kb001",
        "create_time": get_now_time()
    }

    es.index(index=index_name, body=doc)

    response = es.search(index=index_name, body={"query": {"match": {"kbase_id": "kb001"}}})
    print(response)


def moni_insert_data():
    es = ElasticSearchEngine()
    # es.create()
    file = "D:\\桌面\\赘婿.txt"
    with open(file, "r", encoding="utf8") as f:
        lines = f.readlines()
        text = ""
        index = 0
        for line in lines[:10]:
            print(line)
            if len(line.strip()) > 0:
                text += line
                if len(text) > 100:
                    # 定义文档
                    doc = {
                        "doc_name": "赘婿",
                        "doc_id": "sc001",
                        "content_id": "dl00{}".format(index),
                        "title": "第一章_黑名单{}".format(index),
                        "content": text,
                        "summary": text[:10],
                        "kbase_id": "kb002",
                        "create_time": get_now_time()
                    }
                    index += 1
                    text = ""
                    print(es.insert(document=doc))


def moni_search_data():
    es = ElasticSearchEngine()

    # query = {"query": {"match_all": {} } }
    # res = es.search(query)
    # items = res.get("hits", {}).get("hits", [])
    # for item in items:
    #     print(item)
    #     print("-" * 20)

    # cnt = es.count()
    # print(cnt)

    for _ in range(5):
        text = input("you:")
        items = es.search_firewall(text, top_k=20)
        print(items)


def moni_delete_index():
    es = ElasticSearchEngine()
    es.drop()


def moni_reindex():
    es = ElasticSearchEngine()
    es.reindex("firewall_sc", "firewall")


def moni_chachong():
    es = ElasticSearchEngine()
    es.find_filed("kbase_id", "kb001")


def moni_delete_kbase():
    es = ElasticSearchEngine()
    num = es.count()
    print("num=", num)
    es.delete_by_kbase_id(kbase_id="kb001")
    num = es.count()
    print("num=", num)


def create_rag_kbase():
    es = ElasticSearchEngine()
    mapping = {
        "mappings": {
            "properties": {
                "name": {  # 知识库名
                    "type": "text",
                    "analyzer": "ik_max_word"
                },
                "kbase_id": {  # 知识库ID
                    "type": "keyword"
                },
                "create_time": {  # 创建时间
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "update_time": {  # 更新时间
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                }
            }
        }
    }
    es.create(index_name="rag_kbase", mapping=mapping)


def moni_synonyms():
    synonyms = ["沈杨,沈老师,杨老师"]
    doc = {
        "name": "沈杨的爱情故事"
        # "doc_name": "测试同义词",
        # "content_id": "sc001",
        # "title": "第一章_我爱上",
        # "content": "沈杨的爱情故事",
        # "summary": "精彩故事",
        # "kbase_id": "sc",
        # "create_time": get_now_time()
    }
    es = ElasticSearchEngine()
    es.create_sc()
    # es.add_synonyms(synonyms=synonyms)
    es.insert(document=doc, index_name="test_sc")

    for _ in range(5):
        query1 = input("you:")
        query = {
            "query": {
                "multi_match": {
                    "query": query1,
                    "fields": ["name"]
                }
            },
            "size": 5
        }
        print(es.search(query=query, index_name="test_sc"))


if __name__ == '__main__':
    # 知识库表创建
    # create_rag_kbase()

    # moni_insert_data()

    # moni_search_data()

    # moni_delete_index()

    # moni_delete_kbase()

    # moni_chachong()

    client = ElasticSearchEngine()
    response = client.search_firewall('', top_k=10, index_name=es_doc_index,
                                      content_ids='9424626814000016')
    print(response)

    hits = response.get("hits", {}).get("hits", [])
    # print(hits)
    data = []
    for hit in hits:
        data.append(hit.get("_source", {}))
    print(data)
    # print(es.is_exist("firewall_sc"))

    # moni_synonyms()
