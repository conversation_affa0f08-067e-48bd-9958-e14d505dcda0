import torch
from src.config.constant import log
from src.config.sys_config import BASE_PATH
from transformers import AutoTokenizer, AutoModelForSequenceClassification


tokenizer = AutoTokenizer.from_pretrained(f"{BASE_PATH}/src/calculate/reranker_model")
model = AutoModelForSequenceClassification.from_pretrained(f"{BASE_PATH}/src/calculate/reranker_model")

device = 'cpu'
model.to(device)

log.info("【重排序模型】:加载成功")

sentence_pairs = [
    ("apples", "I like apples"), 
    ("apples", "I like oranges"),
    ("apples", "Apples and oranges are fruits")
]


def getRerankerSources(query, contentList):
    '''
        对召回的文档片段进行重排序
    :param query: 用户问题
    :param contentList: 召回的文档片段list
    :return: n*1维度的向量
    '''
    sentence_pairs = []
    for content in contentList:
        sentence_pairs.append((query, content))
    inputs = tokenizer(sentence_pairs, padding=True, truncation=True, max_length=512, return_tensors="pt")
    inputs_on_device = {k: v.to(device) for k, v in inputs.items()}

    scores = model(**inputs_on_device, return_dict=True).logits.view(-1,).float()
    scores = torch.sigmoid(scores)
    scores = scores.cpu().detach().numpy()
    return scores

def rerank_by_score(total_list, score_list):
    log.info(f'重排后的列表为： {score_list}')
    # 根据 score_list 中的分值进行排序
    sorted_list = sorted(zip(total_list, score_list), key=lambda x: x[1], reverse=True)

    # 提取排序后的字典
    sorted_list = [item[0] for item in sorted_list]
    return sorted_list
