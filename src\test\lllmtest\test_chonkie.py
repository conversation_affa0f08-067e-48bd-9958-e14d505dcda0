import pdfplumber
from chonkie import SentenceChunker

from transformers import  AutoTokenizer
import pymupdf4llm

md_text = pymupdf4llm.to_markdown("C:/Users/<USER>/Downloads/关于发布《奇安信集团员工手册》V4.0 的通知.pdf")
print(md_text)

tokenizer = AutoTokenizer.from_pretrained(f"../../calculate/embedding_model")
chunker = SentenceChunker(
    tokenizer=tokenizer,
    chunk_size=512,
    chunk_overlap=128,
    # mode="spacy",  # 'simple' 或 'spacy'
    min_sentences_per_chunk=1
)

# 分块文本
chunks = chunker.chunk(md_text)

# 查看分块内容
for chunk in chunks:
    print(f"Chunk: {chunk.text}")
    print(f"Tokens: {chunk.token_count}")