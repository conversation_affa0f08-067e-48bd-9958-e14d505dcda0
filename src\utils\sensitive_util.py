import itertools
from abc import ABC, abstractmethod
from typing import List

import jieba
from opencc import OpenCC

from src.config.constant import *
from src.utils.sizeof_util import get_size
from src.utils.str_util import *


# 敏感词过滤抽象基类
class Filter(ABC):
    # 添加敏感词
    @abstractmethod
    def add(self, keywords):
        pass

    # 文本敏感词过滤
    @abstractmethod
    def filter(self, message, repl="*"):
        pass


# 敏感词过滤：分词再匹配
class SegmentFilter(Filter):
    def __init__(self):
        self.keywords = set([])

    # 添加敏感词
    def add(self, keywords: List[str]):
        for word in keywords:
            if len(word.strip()) == 0:
                continue
            self.keywords.add(word.strip())
            jieba.add_word(word)
        log.info(f"SegmentFilter 占用{get_size(self.keywords)}字节")

    # 文本过滤敏感词
    def filter(self, message, repl="*"):
        result = jieba.lcut(message)
        # print(result)
        for word in self.keywords:
            result = [item.replace(word, "*" * len(word)) for item in result]
        return "".join(result)

    # 文本敏感词扫描
    def scan(self, message):
        result = jieba.lcut(message)

        matches = []
        for word in self.keywords:
            if word in result:
                matches.append(word)

        return list(set(matches))


# 敏感词过滤：暴力匹配
class BasicFilter(Filter):
    def __init__(self):
        self.keywords = set([])

    # 添加敏感词
    def add(self, words: List[str]):
        for word in words:
            if len(word.strip()) == 0:
                continue
            self.keywords.add(word.strip())
        log.info(f"BasicFilter 占用{get_size(self.keywords)}字节")

    # 文本过滤敏感词
    def filter(self, message, repl="*"):
        result = message
        for word in self.keywords:
            result = result.replace(word, repl * len(word))
        return result

    # 文本敏感词扫描
    def scan(self, message):
        matches = []
        for word in self.keywords:
            if word in message:
                matches.append(word)

        return list(set(matches))


# 敏感词过滤：繁体字匹配
class TraditionalFilter(Filter):
    def __init__(self):
        self.keywords = set([])

    # 添加敏感词，繁体
    def add(self, keywords: List[str]):
        converter = OpenCC('s2t')
        for word in keywords:
            if len(word.strip()) == 0:
                continue
            traditional_word = converter.convert(word)
            self.keywords.add(word)
            self.keywords.add(traditional_word)
        log.info(f"TraditionalFilter 占用{get_size(self.keywords)}字节")

    # 文本过滤敏感词
    def filter(self, message, repl="*"):
        result = message
        for word in self.keywords:
            result = result.replace(word, repl * len(word))
        return result

    # 文本敏感词扫描
    def scan(self, message):
        matches = []
        for word in self.keywords:
            if word in message:
                matches.append(word)

        return list(set(matches))


# 敏感词过滤：同音字匹配
class SimilarWordFilter(Filter):
    def __init__(self):
        self.keywords = set([])
        self.similar_words = self.similar_init()

    # 初始化相似字
    def similar_init(self):
        result = list()
        filepath = os.path.join(dataload_path, "pinyin.txt")
        print(filepath)
        with open(filepath, "r", encoding="utf8") as f:
            lines = f.readlines()
            for line in lines:
                if len(line.strip()) == 0:
                    continue
                index_start, index_end = find_first_and_second_index_from_right(line, "\"")
                # print(line)
                # print(line[end + 1: start])
                result.append(line[index_end + 1: index_start])

        return result

    # 获取同音字
    def get_homophone(self, word):
        result = ""
        for item in self.similar_words:
            if word in item:
                result += item
        # print(result)
        if result:
            return list(result)
        else:
            return [word]

    # 获取同音词
    def get_similar_words(self, words):
        result = list()
        for index, word in enumerate(words):
            result.extend([words[:index] + item + words[index + 1:] for item in self.get_homophone(word)])
        # print(result)
        return result

    # 添加敏感词，同音字
    def add(self, keywords: List[str]):
        for word in keywords:
            # print(word)
            if len(word.strip()) == 0:
                continue
            if 1 < len(word) <= 3:
                self.keywords.update(self.get_similar_words(word))
            else:
                self.keywords.add(word)
        log.info(f"SimilarWordFilter 占用{get_size(self.keywords)}字节")

    # 文本过滤敏感词
    def filter(self, message, repl="*"):
        result = message
        for word in self.keywords:
            result = result.replace(word, repl * len(word))
        return result

    # 文本敏感词扫描
    def scan(self, message):
        matches = []
        for word in self.keywords:
            if word in message:
                matches.append(word)

        return list(set(matches))


# 敏感词过滤：敏感词排列
class PermutationFilter(Filter):
    def __init__(self):
        self.keywords = set([])

    # 词语的排列组合， 123 -> 123,132,213,231,312,321
    def get_permutation(self, word):
        # result = list()
        permutations = list(itertools.permutations(list(word)))
        result = ["".join(item) for item in permutations]
        return result

    # 添加敏感词，排列组合
    def add(self, keywords: List[str]):
        for word in keywords:
            if len(word.strip()) == 0:
                continue
            if is_all_chinese(word):
                if len(word) <= 3:
                    permutations = self.get_permutation(word)
                    self.keywords.update(permutations)
                else:
                    self.keywords.add(word)
            else:
                self.keywords.add(word)
        log.info(f"PermutationFilter 占用{get_size(self.keywords)}字节")

    # 文本过滤敏感词
    def filter(self, message, repl="*"):
        result = message
        for word in self.keywords:
            result = result.replace(word, repl * len(word))
        return result

    # 文本敏感词扫描
    def scan(self, message):
        matches = []
        for word in self.keywords:
            if word in message:
                matches.append(word)

        return list(set(matches))


# 敏感词过滤：敏感词干扰符
class NoiseFilter(Filter):
    def __init__(self):
        self.keyword_chains = {}  # 敏感词库
        self.delimit = '\x00'  # 叶节点标识符

    # 添加敏感词
    def add(self, keywords: List[str]):
        for chars in keywords:
            if len(chars) == 0:
                continue
            level = self.keyword_chains  # 获取当前敏感词
            for i in range(len(chars)):
                if chars[i] in level:
                    level = level[chars[i]]
                else:
                    if not isinstance(level, dict):
                        break
                    for j in range(i, len(chars)):
                        level[chars[j]] = {}
                        last_level, last_char = level, chars[j]
                        level = level[chars[j]]
                    last_level[last_char] = {self.delimit: 0}  # 叶节点
                    break
            if i == len(chars) - 1:
                level[self.delimit] = 0  # 叶节点
        log.info(f"NoiseFilter 占用{get_size(self.keyword_chains)}字节")

    # 文本过滤敏感词
    def filter(self, message, repl="*"):
        if not isinstance(message, str):
            message = message.decode("utf8")
        # message = message.lower()
        ret = []
        start = 0
        while start < len(message):
            level = self.keyword_chains
            step_ins = 0  # 查到敏感词的长度
            # 逐字判断
            for char in message[start:]:
                if is_other(char) and step_ins > 0:
                    step_ins += 1
                    continue
                if char in level:
                    step_ins += 1
                    # 判断有无叶节点，有则返回，无则向下查找，查找最短敏感词
                    if self.delimit not in level[char]:
                        level = level[char]
                    else:
                        ret.append(repl * step_ins)
                        start += step_ins - 1
                        break
                else:
                    ret.append(message[start])
                    break
            else:
                ret.append(message[start])
            start += 1
        return "".join(ret)

    # 文本敏感词扫描
    def scan(self, message):
        matches = []
        start = 0
        while start < len(message):
            level = self.keyword_chains
            step_ins = 0  # 查到敏感词的长度
            # 逐字判断
            for char in message[start:]:
                if is_other(char) and step_ins > 0:
                    step_ins += 1
                    continue
                if char in level:
                    step_ins += 1
                    # 判断有无叶节点，有则返回，无则向下查找，查找最短敏感词
                    if self.delimit not in level[char]:
                        level = level[char]
                    else:
                        matches.append(message[start: start + step_ins])
                        break
                else:
                    break
            start += 1
        return matches


class MyFilter1():
    def __init__(self):
        self.keyword = []
        self.keyword_chains = {}  # 敏感词库
        self.delimit = '\x00'  # 叶节点标识符

    # 添加敏感词
    def add(self, keyword):
        if not isinstance(keyword, str):
            keyword = keyword.decode("utf8")
        keyword = keyword.lower()
        chars = keyword.strip()
        if not chars:
            return
        self.keyword.append(chars)
        level = self.keyword_chains  # 获取当前敏感词
        for i in range(len(chars)):
            if chars[i] in level:
                level = level[chars[i]]
            else:
                if not isinstance(level, dict):
                    break
                for j in range(i, len(chars)):
                    level[chars[j]] = {}
                    last_level, last_char = level, chars[j]
                    level = level[chars[j]]
                last_level[last_char] = {self.delimit: 0}  # 叶节点
                break
        if i == len(chars) - 1:
            level[self.delimit] = 0  # 叶节点

    # 读取文件，加载敏感词
    def parser(self, path):
        with open(path, encoding="utf8") as f:
            for word in f:
                self.add(word.strip())

    # 文本过滤敏感词
    def filter(self, message, repl="*"):
        if not isinstance(message, str):
            message = message.decode("utf8")
        message = message.lower()
        ret = []
        start = 0
        while start < len(message):
            level = self.keyword_chains
            step_ins = 0  # 查到敏感词的长度
            # 逐字判断
            for char in message[start:]:
                if char in level:
                    step_ins += 1
                    # 判断有无叶节点，有则返回，无则向下查找，查找最短敏感词
                    if self.delimit not in level[char]:
                        level = level[char]
                    else:
                        ret.append(repl * step_ins)
                        start += step_ins - 1
                        break
                else:
                    ret.append(message[start])
                    break
            else:
                ret.append(message[start])
            start += 1
        return "".join(ret)

    # 提取敏感词
    def get_sensitive_words(self, content: str):
        content = content.lower()
        result = []  # 结果
        start = 0
        while start < len(content):
            sex_words = self.keyword_chains
            find_word = ""
            num = start
            for index in range(len(content[start:])):
                start += 1
                word = content[num:][index]
                if is_other(word):
                    continue
                if word in sex_words:
                    find_word += word
                    if self.delimit in sex_words[word]:
                        result.append(find_word)
                    else:
                        sex_words = sex_words[word]
                else:
                    break
        return result

    def filter1(self, content):
        if not isinstance(content, str):
            content = content.decode("utf8")
        content = content.lower()
        result = content
        for word in self.keyword:
            result = result.replace(word, "*" * len(word))
        return result


def test():
    fw = SimilarWordFilter()
    fw.add(["中国"])


def test_time():
    import time
    kws = open("D:\\桌面\\word.txt", "r", encoding="utf8").read().split("\n")
    print(len(kws))
    filter = MyFilter1()
    filter.parser("D:\\桌面\\word.txt")

    message = open("D:\\桌面\\data.txt", "r", encoding="utf8").read()
    message = message[:100000]

    start = time.time()
    filter.filter1(message)
    end = time.time()
    print("replace time:", end - start)

    start = time.time()
    filter.filter(message)
    end = time.time()
    print("tree time:", end - start)


def test_noise():
    fw = NoiseFilter()
    kws = open("D:\\桌面\\word.txt", "r", encoding="utf8").read().split("\n")
    fw.add(kws)
    message = "我要去打鱼……---,。叉，很有风&险。"
    res = fw.filter(message)
    print(message)
    print(res)


def test_bf():
    my = MyFilter1()
    my.add("中国人")
    my.add("国共")

    s = "我是中国共产党，奥利给"
    res = my.filter(s)
    print(res)


if __name__ == '__main__':
    # test()
    # test_time()
    # test_noise()
    test_bf()

    # kws = open("D:\\桌面\\keyword.txt", "r", encoding="utf8").read().split("\n")
    # print(kws)
    # content = "习近平参加大会TMD。"
    # print(content)
    # fw = BasicFilter()
    # fw.add(kws)
    # print(fw.filter(content))

    # gfw = MyFilter1()
    # gfw.parser("D:\\桌面\\keyword.txt")
    #
    # message = "习近平参加大会TMD"
    # message = open("D:\\桌面\\data.txt", "r", encoding="utf8").read()
    # message = message[:100000]
    # print(len(message))
    # import time
    # start = time.time()
    # gfw.filter1(message)
    # end = time.time()
    # print("cost:", end - start)
    # start = time.time()
    # gfw.filter(message)
    # end = time.time()
    # print("cost:", end - start)
    # print(gfw.get_sensitive_words(message))
    # print("cost:", time.time() - end)
