# -*- coding: utf-8 -*-
"""
Create Time: 2024/6/20 15:30
Author: liuyigong
"""
from src.config.constant import *
# import jiagu
import sys
import pickle
import traceback


# ----------------------------------------------基础组件和本地词表---------------------------------------------
stopwords = []
stop_zc = []
stop_lac = []
stop_txt = []

def init_local_word_list():
    global stopwords, stop_zc, stop_lac

    log.info("start to init_local_word_list")
    stopwords = [i.replace('\n', '') for i in open(os.path.join(dataload_path, "stopwords.txt"), "r", encoding="utf-8")]  # 停用词
    stop_zc = [i.replace('\n', '') for i in open(os.path.join(dataload_path, "stop_zhuci.txt"), "r", encoding="utf-8")] # 停用语气助词
    stop_lac = [i.replace('\n', '') for i in open(os.path.join(dataload_path, "stopword_lac.txt"), "r", encoding="utf-8")] # lac停用词
    stop_txt = [i.replace('\n', '') for i in open(os.path.join(dataload_path, "stopword_txt.txt"), "r", encoding="utf-8")] # txt文本停用词
    intent_yes= [i.replace('\n', '') for i in open(os.path.join(dataload_path, "intent_yes.txt"), "r", encoding="utf-8")] # 表示确认的意图的短词


# 加载同义词
synMap = dict()  # 同义词字典

synonyms_list = []
def load_synonyms():
    with open(os.path.join(dataload_path, "synonyms.txt")) as f:
        lines = f.readlines()
        for line in lines:
            synonyms_list.append(line.spint(","))