# -*- coding: utf-8 -*-
"""
Create Time: 2024/9/10 14:04
Author: liuyigong
Description: 异步Milvus工具类
"""
import random
import asyncio
import time
from pymilvus import (
    connections,
    utility,
    Collection,
    FieldSchema,
    CollectionSchema,
    DataType
)

from src.config.constant import *
from src.config.constant import milvus_ip, milvus_port, milvus_username, milvus_password
from src.utils.singleton_util import Singleton


@Singleton
class AsyncMilvusClient:
    """异步Milvus客户端"""
    
    def __init__(self, host="**********", port="19530", user="root", passwd="admin@2024"):
        """初始化"""
        self.host = milvus_ip
        self.port = milvus_port
        self.user = milvus_username
        self.password = milvus_password
        self.collection = None
        self.connection = None
        self.collections = {}  # 存储已创建的集合对象
        # 确保单例实例可以被正确访问
        global asyncMilvusClient
        asyncMilvusClient = self
    
    async def get_connection(self, alias="default"):
        """
        获取或创建Milvus连接
        
        参数:
            alias: 连接别名
            
        返回:
            连接对象
        """
        if self.connection is None:
            await self.connect(alias)
        return self.connection
    
    async def connect(self, alias="default") -> None:
        """
        连接到Milvus服务器
        
        参数:
            alias: 连接别名
        """
        log.info(f"[sc]连接数据库{self.host}：{self.port}")
        self.connection = await asyncio.to_thread(
            connections.connect,
            alias=alias,
            host=self.host,
            port=self.port,
            user=self.user,
            password=self.password
        )
    
    async def create_collection(self, collection_name="firewall_sc", fields=None, index_filed=None, description="防火墙") -> None:
        """
        创建集合
        
        参数:
            collection_name: 集合名称
            fields: 字段定义
            index_filed: 索引字段
            description: 集合描述
        """
        # 确保连接已建立
        await self.get_connection()
        
        log.info(f"[sc]创建集合{collection_name}")
        
        # 检查集合是否存在
        if await self.exist_collection(collection_name):
            log.info(f"[sc]集合{collection_name}已存在")
            return
        
        # 设置默认字段
        if fields is None:
            fields = [
                FieldSchema(name="content_id", dtype=DataType.INT64, is_primary=True),
                FieldSchema(name="doc_id", dtype=DataType.INT64),
                FieldSchema(name="kbase_id", dtype=DataType.INT64),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=768)
            ]
        
        # 创建集合
        schema = CollectionSchema(fields=fields, description=description)
        self.collection = await asyncio.to_thread(Collection, name=collection_name, schema=schema)
        
        # 创建索引
        index_params = {
            "index_type": "IVF_FLAT",
            "params": {"nlist": 100},
            "metric_type": "L2"
        }
        
        if index_filed is None:
            index_filed = "embedding"
        
        await asyncio.to_thread(self.collection.create_index, field_name=index_filed, index_params=index_params)
        log.info(f"[sc]创建集合{collection_name}:{description}")
    
    async def insert_data(self, records, collection_name="firewall_sc") -> None:
        """
        插入数据
        
        参数:
            records: 要插入的记录
            collection_name: 集合名称
        """
        # 获取集合对象
        collection = await self.get_collection(collection_name)
        
        if collection is None:
            log.error("[sc]数据库连接失败")
            return
        
        await asyncio.to_thread(collection.insert, records)
        # await asyncio.to_thread(collection.flush)
        log.info(f"[sc]已插入集合{collection_name}共{len(records)}条记录")
    
    async def search_data(self, query, collection_name="firewall_sc", top_k=5, anns_field="embedding",
                         output_fields=["content_id", "doc_id", "kbase_id"], expr=None):
        """
        搜索数据
        
        参数:
            query: 查询向量
            collection_name: 集合名称
            top_k: 返回结果数量
            anns_field: 向量字段名
            output_fields: 输出字段
            expr: 过滤表达式
            
        返回:
            搜索结果
        """
        # 获取集合对象
        collection = await self.get_collection(collection_name)
        
        log.info(f"[sc]搜索集合：{collection_name}，文本：{query[0:min(3, len(query))]}...，限制数量：{top_k}")
        
        query_record = query  # ToDo
        log.info(f"[sc]搜索向量：{query_record[0:min(3, len(query_record))]}...")
        
        await asyncio.to_thread(collection.load)
        
        # 定义搜索参数
        search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
        
        results = await asyncio.to_thread(
            collection.search,
            data=[query_record],  # 查询向量
            anns_field=anns_field,
            param=search_params,
            limit=top_k,
            output_fields=output_fields,
            expr=expr
        )
        
        return results
    
    async def exist_collection(self, collection_name) -> bool:
        """
        判断集合是否存在
        
        参数:
            collection_name: 集合名称
            
        返回:
            是否存在
        """
        # 确保连接已建立
        await self.get_connection()
        
        try:
            return await asyncio.to_thread(utility.has_collection, using="default", collection_name=collection_name)
        except Exception:
            return False
    
    async def delete_data(self, expr=None, collection_name="firewall_sc") -> None:
        """
        删除数据
        
        参数:
            expr: 过滤表达式
            collection_name: 集合名称
        """
        if expr is None:
            return
            
        # 获取集合对象
        collection = await self.get_collection(collection_name)
        
        log.info("[sc]milvus删除数据")
        
        await asyncio.to_thread(collection.load)
        await asyncio.to_thread(collection.delete, expr=expr)
        log.info(f"[sc]删除集合{collection_name}中：{expr}")
    
    async def drop_collection(self, collection_name="firewall_sc") -> None:
        """
        删除集合
        
        参数:
            collection_name: 集合名称
        """
        # 获取集合对象
        collection = await self.get_collection(collection_name)
        
        await asyncio.to_thread(collection.drop)
        
        # 从缓存中移除
        if collection_name in self.collections:
            del self.collections[collection_name]
            
        log.info(f"[sc]删除集合{collection_name}")
    
    async def close(self) -> None:
        """关闭连接"""
        if self.connection:
            await asyncio.to_thread(self.connection.close)
            self.connection = None

    async def get_collection(self, collection_name="firewall_sc"):
        """
        获取或创建Collection对象
        
        参数:
            collection_name: 集合名称
            
        返回:
            Collection对象
        """
        # 确保连接已建立
        await self.get_connection()
        
        # 如果集合对象已存在，直接返回
        if collection_name in self.collections:
            return self.collections[collection_name]
        
        # 创建新的集合对象
        collection = await asyncio.to_thread(Collection, name=collection_name)
        self.collections[collection_name] = collection
        return collection

# 创建单例实例
asyncMilvusClient = AsyncMilvusClient()
log.info(f"[sc]AsyncMilvusClient 单例实例已初始化: {id(asyncMilvusClient)}")
# 确保可以从模块中直接导入
__all__ = ['AsyncMilvusClient', 'asyncMilvusClient']


async def test():
    """测试异步Milvus功能"""
    # 连接服务器
    await asyncMilvusClient.connect()
    
    # 定义字段
    fields = [
        FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
        FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=128)
    ]
    
    # 创建集合
    collection_name = "sc_example_async"
    if not await asyncMilvusClient.exist_collection(collection_name):
        await asyncMilvusClient.create_collection(collection_name=collection_name, fields=fields, description="测试集合")
    
    # 插入向量
    async def insert_vectors():
        # 随机向量数据
        vectors = [[random.random() for _ in range(128)] for __ in range(10)]
        # 插入数据
        await asyncMilvusClient.insert_data(vectors, collection_name=collection_name)
        print("插入成功")
    
    # 搜索向量
    async def search_vector(vector, limit):
        results = await asyncMilvusClient.search_data(
            query=vector,
            collection_name=collection_name,
            top_k=limit,
            output_fields=[]
        )
        
        for result in results:
            print(result)
    
    await insert_vectors()
    
    # 生成随机向量进行搜索
    vector = [random.random() for _ in range(128)]
    await search_vector(vector, 5)
    
    # 关闭连接
    await asyncMilvusClient.close()

async def create_test():
    """测试创建集合"""
    await asyncMilvusClient.connect()
    await asyncMilvusClient.create_collection(collection_name="sc_test_async")
    await asyncMilvusClient.close()

async def test1():
    """测试集合是否存在"""
    await asyncMilvusClient.connect()
    col = await asyncMilvusClient.exist_collection("firewall_sc1")
    if col:
        print("Yes")
    else:
        print("no")
    await asyncMilvusClient.close()

if __name__ == '__main__':
    import asyncio
    asyncio.run(test())









