from openai import OpenAI
import httpx
openai_api_key = "123"
openai_api_base = "http://10.51.97.36:10010/v1"

deepthink_button = 'False'
#deepthink_button = 'True'
MESSAGES = [
    {"role": "system", "content": f"You are an AI assistant named <PERSON><PERSON><PERSON> created by QiAnXin Inc. Your answer should be friendly, unbiased, faithful, informative and detailed.\n<deepthink_button>{deepthink_button}</deepthink_button>\nToday is 2025-03-24."},
    #{"role": "system", "content": "You are a helpful assistant.\nToday is 2025-03-24."},
    #{"role": "user",  "content": "What's the temperature in San Francisco now? How about tomorrow?"},
    {"role": "user",  "content": "什么是sql注入"},
    #{"role": "user",  "content": "你是谁"},
]
http_client = httpx.Client(verify=False)

TOOLS = []
tools = TOOLS
messages = MESSAGES[:]
client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
    http_client=http_client
)

response = client.chat.completions.create(
        model="functioncall",
        messages=messages,
        tools=tools,
        temperature=0.6,
        top_p=0.95,
        max_tokens=512,
        extra_body={
            "repetition_penalty": 1.0,
        },
    )


print('-------------model_answer---------------')
content = response.choices[0].message.model_dump()
print(content)