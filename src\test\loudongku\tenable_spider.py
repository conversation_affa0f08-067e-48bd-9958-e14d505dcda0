import requests
import json
import csv
from bs4 import BeautifulSoup
from tqdm import tqdm
import os
# 代理服务器配置
proxies_sh = {
    'http': 'http://proxy.sh.qianxin-inc.cn:3128',
    'https': 'http://proxy.sh.qianxin-inc.cn:3128',
}

# 翻译 API 的 URL
translate_url = "https://findmyip.net/api/translate.php"


# 翻译函数
def translate_text(text):
    params = {
        "text": text
    }
    try:
        response = requests.get(f"{translate_url}?text={text}", params=params, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200 and "data" in result:
                # print(result.get('data').get('translate_result', text))
                return result.get('data').get('translate_result', text)
    except requests.RequestException as e:
        print(f"翻译请求失败: {e}")

    print("翻译失败，返回原始文本")
    return text  # 如果翻译失败，返回原始文本


# 爬取并翻译 CVE 数据
def crawl_and_translate_cves(base_url, start_page, end_page, src_html, src_form):
    # 创建目录
    os.makedirs(src_html, exist_ok=True)
    os.makedirs(src_form, exist_ok=True)

    # 每隔 100 页保存一个 CSV 文件
    csv_file_index = 1
    csv_file_path = os.path.join(src_form, f'cve_data_{csv_file_index}.csv')
    csv_file = open(csv_file_path, 'w', newline='', encoding='utf-8')
    writer = csv.writer(csv_file)
    writer.writerow(['CVE ID', 'Description', 'Severity'])

    for page in tqdm(range(start_page, end_page + 1), desc="Crawling Pages"):
        url = f"{base_url}?page={page}"
        try:
            response = requests.get(url, proxies=proxies_sh, timeout=10)
            if response.status_code == 200:
                # 保存原始 HTML 内容
                html_file_path = os.path.join(src_html, f'page_{page}.html')
                with open(html_file_path, 'w', encoding='utf-8') as html_file:
                    html_file.write(response.text)

                # 解析 HTML 内容
                soup = BeautifulSoup(response.text, 'html.parser')
                script_tag = soup.find('script', id="__NEXT_DATA__")
                if script_tag:
                    json_data = json.loads(script_tag.string)
                    cves = json_data['props']['pageProps']['cves']
                    for cve in tqdm(cves, desc=f"Processing Page {page}", leave=False):
                        cve_id = cve['_id']
                        description = translate_text(cve['_source']['description'])
                        severity = translate_text(cve['_source']['severity'])
                        # print(cve_id, description, severity)
                        # break
                        writer.writerow([cve_id, description, severity])
                else:
                    print(f"第 {page} 页未找到包含 CVE 数据的 script 标签。")
            else:
                print(f"请求第 {page} 页失败，状态码: {response.status_code}")
        except requests.RequestException as e:
            print(f"请求第 {page} 页失败: {e}")
        # break
        # 每隔 100 页关闭当前 CSV 文件并新建一个
        if page % 100 == 0:
            csv_file.close()
            csv_file_index += 1
            csv_file_path = os.path.join(src_form, f'cve_data_{csv_file_index}.csv')
            csv_file = open(csv_file_path, 'w', newline='', encoding='utf-8')
            writer = csv.writer(csv_file)
            writer.writerow(['CVE ID', 'Description', 'Severity'])

    # 关闭最后一个 CSV 文件
    csv_file.close()
    print("所有数据已成功保存到指定路径下。")

# 主函数
if __name__ == "__main__":
    base_url = "https://www.tenable.com/cve/newest"
    start_page = 1
    end_page = 5843  # 根据需要调整结束页码
    src = r'/home/<USER>/apps/nlp/loophole/tenable'
    # src = r'E:\program\python\rag_devc\src\test\loudongku'
    src_form = os.path.join(src, 'form')
    src_html = os.path.join(src, 'html')
    crawl_and_translate_cves(base_url, start_page, end_page, src_html, src_form)



