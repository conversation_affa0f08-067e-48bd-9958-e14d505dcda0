'''

    向量服务

'''

import numpy as np
from src.config.memory import *
from src.config.constant import log
from src.config.sys_config import BASE_PATH

from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks




# 实例化向量模型
pipeline_se = pipeline(Tasks.sentence_embedding, model=f"{BASE_PATH}/src/calculate/model")
log.info("【向量服务】:模型加载成功")



def gen_sentence_vec(question, sentenceList):
    '''
        获取问题和文档的向量表示
    :param question: 问题str
    :param sentenceList: 文档List
    :return: 问题向量的List，文档向量的List
    '''

    log.info(f"【向量服务-问题】:{question}")
    log.info(f"【向量服务-文档】:{sentenceList}")


    # 构造调用模型的数据格式
    inputs = {
        "source_sentence": [question],
        "sentences_to_compare": sentenceList
    }
    # 获取向量化结果
    result = pipeline_se(input=inputs)
    log.info(f"【向量服务-结果】:{result['scores']}")

    return (np.array(result["scores"])/100.0).tolist()

def get_setenceList_vec(sentenceList):
    '''
        获取句子的向量表示
        :param sentenceList: 文档List
        :return: 文档向量的List
    '''
    log.info(f"【句子转向量】:{sentenceList}")

    # 构造调用模型的数据格式
    inputs = {
        "source_sentence": ["测试"],
        "sentences_to_compare": sentenceList
    }
    # 获取向量化结果
    result = pipeline_se(input=inputs)
    # log.info(f"【句子转向量-结果】:{result['text_embedding'][1:10]}")

    return np.array(result["text_embedding"][1:]).tolist()

def get_query_vec(query):
    '''
            获取句子的向量表示
            :param sentenceList: 文档List
            :return: 文档向量的List
        '''
    log.info(f"【问题转向量】:{query}")

    # 构造调用模型的数据格式
    inputs = {
        "source_sentence": [query],
        "sentences_to_compare": [query]
    }
    # 获取向量化结果
    result = pipeline_se(input=inputs)
    # log.info(f"【句子转向量-结果】:{result['text_embedding'][1:10]}")

    return np.array(result["text_embedding"]).tolist()[0]

