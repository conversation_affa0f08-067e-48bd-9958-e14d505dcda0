import json
import requests
import csv
import os
import time
import random
from tqdm import tqdm
from fake_useragent import UserAgent

# JSON文件路径

json_file_name = 'cnvd.json'
json_file_path = os.path.join(r"/home/<USER>/apps/nlp/loophole/changting", json_file_name)

# CSV文件路径
csv_file_path = "vulnerability_info.csv"



# 目标接口URL模板
api_url_template = "https://stack.chaitin.com/api/v2/vuln/list/?offset=0&search={}"


# 初始化UserAgent
ua = UserAgent()

# 请求头
headers = {
    'User-Agent': ua.random,  # 使用随机User-Agent
    'Accept': '*/*',
    'Host': 'stack.chaitin.com',
    'Connection': 'keep-alive',
    # 如果需要，可以动态切换Cookie
    # 'Cookie': 'sl-session=LE9zRHQ2D2gENEJA96ZBsQ=='
}

# 读取JSON文件
with open(json_file_path, "r", encoding="utf-8") as json_file:
    data = json.load(json_file)

# 提取所有CVE编号
cve_ids = set()
for item in data:
    for cve in item.get("cves", []):
        cve_ids.add(cve["cve_id"])

# 检查CSV文件是否存在
csv_exists = os.path.exists(csv_file_path)

# 打开CSV文件（如果不存在则创建）
with open(csv_file_path, "a", newline="", encoding="utf-8") as csv_file:
    writer = csv.writer(csv_file)

    # 如果文件不存在，写入表头
    if not csv_exists:
        writer.writerow([
            "漏洞ID", "漏洞标题", "漏洞描述", "漏洞类型", "严重性等级", "CVE编号", "CNVD编号", "CNNVD编号",
            "影响描述", "修复建议", "披露日期", "利用代码披露日期", "修复日期", "参考链接"
        ])

    # 获取已处理的CVE编号
    processed_cves = set()
    if csv_exists:
        csv_file.seek(0)  # 移动到文件开头
        reader = csv.reader(csv_file)
        next(reader)  # 跳过表头
        for row in reader:
            processed_cves.add(row[5])  # 假设CVE编号在第6列

    # 使用tqdm显示进度条
    for cve_id in tqdm(cve_ids - processed_cves, desc="Crawling CVE Info"):
        api_url = api_url_template.format(cve_id)
        response = requests.get(api_url, headers=headers)

        if response.status_code == 200:
            response_data = response.json()
            if response_data.get("msg") == "success" and response_data.get("data") and response_data["data"].get("list"):
                vulnerabilities = response_data["data"]["list"]
                for vuln in vulnerabilities:
                    useful_data = [
                        vuln.get("id"),
                        vuln.get("title"),
                        vuln.get("summary"),
                        vuln.get("weakness"),
                        vuln.get("severity"),
                        vuln.get("cve_id"),
                        vuln.get("cnvd_id"),
                        vuln.get("cnnvd_id"),
                        vuln.get("impact"),
                        vuln.get("fix_steps"),
                        vuln.get("disclosure_date"),
                        vuln.get("poc_disclosure_date"),
                        vuln.get("patch_date"),
                        vuln.get("references")
                    ]
                    writer.writerow(useful_data)
                    tqdm.write(f"已写入CVE编号 {cve_id} 的漏洞信息到CSV文件。")
            else:
                tqdm.write(f"未找到CVE编号 {cve_id} 的漏洞数据。")
        else:
            tqdm.write(f"请求CVE编号 {cve_id} 的数据失败，状态码：{response.status_code}")

        # 防屏蔽逻辑：随机等待1到3秒
        time.sleep(random.uniform(1, 3))

        # 防屏蔽逻辑：随机更换User-Agent
        headers['User-Agent'] = ua.random

print("所有漏洞信息已处理完毕。")