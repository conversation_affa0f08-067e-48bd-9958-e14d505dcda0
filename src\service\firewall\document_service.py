import random

from src.utils.elasticsearch_util import ElasticSearchEngine
from src.config.constant import log
from src.utils.milvus_util import MilvusClient
from src.calculate.embedding_server import getQueryVec


# 防火墙入库
def es_insert_data(es_data, milvus_data, index_name="firewall_sc", collection_name="firewall_sc"):
    # es入库
    log.info("[sc]es入库")
    es = ElasticSearchEngine()
    if not es.is_exist(index_name):
        log.error("[sc]es库{}索引不存在".format(index_name))
        return {"code": 202, "message": "{}索引不存在".format(index_name)}
    is_success = True
    ids = []
    for item in es_data:
        if not item.get("doc_name", None) or not item.get("doc_id", None) or not item.get("content_id",
            None) or not item.get("content", None)  or not item.get("kbase_id",
            None) or not item.get("create_time", None):
            log.error("[sc]{}中字段缺失".format(item))
            is_success = False
            break
        try:
            result = es.insert(item, index_name=index_name)
            log.info("[sc]es入库：{},结果：{}".format(item, result))
        except Exception as e:
            log.error("[sc]es入库失败：{}".format(e))
            is_success = False
            break
        if not result:
            log.error("[sc]es入库失败：es连接失败")
            is_success = False
            break
        ids.append(result["_id"])
    if not is_success:
        log.info("[sc]es入库失败，准备回滚")
        result1 = es.delete(ids, index_name=index_name)
        if not result1:
            log.error("[sc]回滚失败")
            return {"code": 204, "message": "回滚失败"}
        log.info("[sc]回滚成功：{}".format(result1))
        return {"code": 201, "message": "es入库失败"}
    # milvus入库
    log.info("[sc]milvus入库")
    try:
        milvus = MilvusClient()
        milvus.insert_data(milvus_data, collection_name=collection_name)
    except Exception as e:
        log.info("[sc]milvus入库失败：{}".format(e))
        log.info("[sc]es回滚")
        result1 = es.delete(ids, index_name=index_name)
        if not result1:
            log.error("[sc]回滚失败")
            return {"code": 204, "message": "回滚失败"}
        log.info("[sc]回滚成功：{}".format(result1))
        return {"code": 201, "message": "milvus入库失败"}

    return {"code": 200, "message": "入库成功", "data": {"es": len(es_data), "milvus": len(milvus_data)}}


# es根据字段删除数据
def es_delete_by_field(field, field_value, index_name="firewall_sc"):
    log.info("[sc]es删除：{}={}".format(field, field_value))
    es = ElasticSearchEngine()
    try:
        es.delete_by_filed_batch(field, field_value, index_name)
    except Exception as e:
        log.error("[sc]es报错：", e)
        return {"code": 201, "message": "删除失败"}

    # large_size = 10000
    # query = {
    #     "size": large_size,
    #     "query": {
    #         "term": {
    #             field: field_value
    #         }
    #     }
    # }
    # response = es.search(query, index_name=index_name)
    # # print(response)
    # hits = response.get("hits", {}).get("hits", [])
    # log.info("[sc]es删除：查询结果数={}".format(len(hits)))
    # # i = 1
    # for hit in hits:
    #     # print(i, end=":")
    #     # i += 1
    #     # print(hit["_id"])
    #     log.info("[sc]es删除：_id={}".format(hit["_id"]))
    #     es.delete(hit["_id"], index_name=index_name)
    return {"code": 200, "message": "删除成功"}

# es刷新
def es_data_refresh_manually(index_name):
    es = ElasticSearchEngine()
    try:
        res = es.data_refresh(index_name)
        if res:
            return {"code": 200, "message": "刷新成功"}
        return {"code": 201, "message": "刷新失败"}
    except Exception as e:
        log.error("[sc]es 刷新报错：", e)
        return {"code": 201, "message": "刷新失败"}


# milvus根据字段删除数据
def milvus_delete_by_field(field, field_value, collection_name):
    log.info("[sc]milvus删除：{}={}".format(field, field_value))
    client = MilvusClient()
    # query = {field: field_value}
    query = "{} in [{}]".format(field, field_value)
    client.delete_data(expr=query, collection_name=collection_name)
    return {"code": 200, "message": "删除成功"}


def milvus_insert_test():
    client = MilvusClient()
    # client.create_collection()
    records = []
    for i in range(5):
        doc_id = i
        kbase = 100 + i
        vector = [random.random() for _ in range(768)]
        item = {"content_id": 1, "doc_id": doc_id, "kbase_id": kbase, "embedding": vector}
        records.append(item)
    client.insert_data(records)


def milvus_search_test():
    client = MilvusClient()
    res = client.search_data("我是谁？")
    # print(res["status"])
    items = res[0]
    print(items.ids)
    for i in items:
        print(i.id)
        print(i.fields)
        # print(i["id"])


# es搜索
def es_search(query, index_name="firewall_sc", kbase_ids=[], doc_ids=[], content_ids=''):
    log.info("[sc]es搜索")
    log.info("[sc]搜索内容：{}".format(query))
    client = ElasticSearchEngine()
    response = client.search_firewall(query, top_k=10, index_name=index_name,
                                      kbase_ids=kbase_ids, document_ids=doc_ids,
                                      content_ids=content_ids)
    hits = response.get("hits", {}).get("hits", [])
    # print(hits)
    data = []
    for hit in hits:
        data.append(hit.get("_source", {}))
    return data


# milvus搜索
def milvus_search(query=None, query_vec=None, index_name="firewall_sc", collection_name="firewall_sc", kbase_ids = [], doc_ids = []):
    log.info("[sc]milvus搜索")
    if not query and not query_vec:
        log.info("[sc]milvus搜索内容为空")
        return []
    client = MilvusClient()
    es_client = ElasticSearchEngine()
    if not query_vec:
        query_vec = getQueryVec(query)
        # query_vec = [random.random() for _ in range(768)]
    log.info("[sc]搜索向量：{}...".format(query_vec[0:min(3, len(query_vec))]))
    if kbase_ids and doc_ids:
        expr = "kbase_id in {} and doc_id in {}".format(kbase_ids, doc_ids)
    elif kbase_ids:
        expr = "kbase_id in {}".format(kbase_ids)
    elif doc_ids:
        expr = "doc_id in {}".format(doc_ids)
    else:
        expr = None
    log.info("[sc]expr={}".format(expr))
    response = client.search_data(query_vec, top_k=10, collection_name=collection_name, expr=expr)
    data = []
    items = response[0]
    for item in items:
        # print(item)
        # data.append(item.entity)
        body = {
            "query": {
                "term": {"content_id": item.entity.content_id}
            }
        }
        response1 = es_client.search(query=body, index_name=index_name)
        hits = response1.get("hits", {}).get("hits", [])
        # print(hits)
        for hit in hits:
            data.append(hit.get("_source", {}))
    return data


if __name__ == '__main__':
    # milvus_delete_by_field("doc_id", 147334466)

    # milvus_delete_by_field("doc_id", 3658067579, "firewall_sc")

    # milvus_insert_test()

    # milvus_search_test()

    # delete_by_kabse_id("kb001")

    print(es_search("宁毅"))

    # data = es_search("黑名单")
    # print(data)
    # print(milvus_search("111"))
    pass